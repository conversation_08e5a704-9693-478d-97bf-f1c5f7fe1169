package com.school.management.entity;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Calendar;

/**
 * 入党申请人实体类
 */
public class PartyApplicant {

    private Integer id;                    // 主键ID
    private String name;                   // 姓名
    private String gender;                 // 性别（自动识别）
    private Date birthDate;                // 出生日期（自动识别）
    private Integer age;                   // 年龄（自动计算）
    private String idCard;                 // 身份证号
    private String nativePlace;           // 户籍地
    private String address;                // 地址
    private String phone;                  // 联系电话
    private String grade;                  // 年级
    private Boolean isLeagueMember;        // 是否为共青团员
    private Date applicationDate;          // 申请日期
    private String status;                 // 申请状态
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public PartyApplicant() {}

    public PartyApplicant(String name, String idCard, String nativePlace, String address,
                         String phone, String grade, Boolean isLeagueMember, Date applicationDate, String status) {
        this.name = name;
        this.idCard = idCard;
        this.nativePlace = nativePlace;
        this.address = address;
        this.phone = phone;
        this.grade = grade;
        this.isLeagueMember = isLeagueMember;
        this.applicationDate = applicationDate;
        this.status = status;

        // 自动识别性别、生日和年龄
        if (idCard != null && idCard.length() == 18) {
            this.gender = parseGenderFromIdCard(idCard);
            this.birthDate = parseBirthDateFromIdCard(idCard);
            this.age = calculateAge(this.birthDate);
        }
    }

    // 全参构造函数
    public PartyApplicant(Integer id, String name, String gender, Date birthDate, Integer age, String idCard,
                         String nativePlace, String address, String phone, String grade, Boolean isLeagueMember,
                         Date applicationDate, String status, String remarks, Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.name = name;
        this.gender = gender;
        this.birthDate = birthDate;
        this.age = age;
        this.idCard = idCard;
        this.nativePlace = nativePlace;
        this.address = address;
        this.phone = phone;
        this.grade = grade;
        this.isLeagueMember = isLeagueMember;
        this.applicationDate = applicationDate;
        this.status = status;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    // 身份证解析方法

    /**
     * 从身份证号解析性别
     * @param idCard 身份证号
     * @return 性别（男/女）
     */
    private String parseGenderFromIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return null;
        }
        // 身份证第17位（倒数第2位）为性别标识，奇数为男，偶数为女
        int genderCode = Integer.parseInt(idCard.substring(16, 17));
        return genderCode % 2 == 1 ? "男" : "女";
    }

    /**
     * 从身份证号解析出生日期
     * @param idCard 身份证号
     * @return 出生日期
     */
    private Date parseBirthDateFromIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return null;
        }
        try {
            // 身份证第7-14位为出生日期
            String birthStr = idCard.substring(6, 14);
            int year = Integer.parseInt(birthStr.substring(0, 4));
            int month = Integer.parseInt(birthStr.substring(4, 6));
            int day = Integer.parseInt(birthStr.substring(6, 8));

            Calendar calendar = Calendar.getInstance();
            calendar.set(year, month - 1, day); // 月份从0开始
            return calendar.getTime();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算年龄
     * @param birthDate 出生日期
     * @return 年龄
     */
    private Integer calculateAge(Date birthDate) {
        if (birthDate == null) {
            return null;
        }
        Calendar birth = Calendar.getInstance();
        birth.setTime(birthDate);
        Calendar now = Calendar.getInstance();

        int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
        if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
            age--;
        }
        return age;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public Date getBirthDate() { return birthDate; }
    public void setBirthDate(Date birthDate) { this.birthDate = birthDate; }

    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }

    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) {
        this.idCard = idCard;
        // 当设置身份证号时，自动解析性别、生日和年龄
        if (idCard != null && idCard.length() == 18) {
            this.gender = parseGenderFromIdCard(idCard);
            this.birthDate = parseBirthDateFromIdCard(idCard);
            this.age = calculateAge(this.birthDate);
        }
    }

    public String getNativePlace() { return nativePlace; }
    public void setNativePlace(String nativePlace) { this.nativePlace = nativePlace; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getGrade() { return grade; }
    public void setGrade(String grade) { this.grade = grade; }

    public Boolean getIsLeagueMember() { return isLeagueMember; }
    public void setIsLeagueMember(Boolean isLeagueMember) { this.isLeagueMember = isLeagueMember; }

    public Date getApplicationDate() { return applicationDate; }
    public void setApplicationDate(Date applicationDate) { this.applicationDate = applicationDate; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }

    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }

    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "PartyApplicant{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", age=" + age +
                ", idCard='" + idCard + '\'' +
                ", nativePlace='" + nativePlace + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", grade='" + grade + '\'' +
                ", isLeagueMember=" + isLeagueMember +
                ", applicationDate=" + applicationDate +
                ", status='" + status + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
