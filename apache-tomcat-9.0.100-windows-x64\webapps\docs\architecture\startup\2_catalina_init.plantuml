@startuml

' Licensed to the Apache Software Foundation (ASF) under one or more
' contributor license agreements.  See the NOTICE file distributed with
' this work for additional information regarding copyright ownership.
' The ASF licenses this file to You under the Apache License, Version 2.0
' (the "License"); you may not use this file except in compliance with
' the License.  You may obtain a copy of the License at
'
'     http://www.apache.org/licenses/LICENSE-2.0
'
' Unless required by applicable law or agreed to in writing, software
' distributed under the License is distributed on an "AS IS" BASIS,
' WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
' See the License for the specific language governing permissions and
' limitations under the License.

hide footbox
skinparam style strictuml

activate Catalina

Catalina -> Server ++: init()

Server -> Server: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)

Server -> Server ++: initInternal()

Server -> "Global\nNaming\nResources" as GNR ++: init()
GNR -> GNR: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)
GNR -> GNR: initInternal()
GNR -> GNR: fireLifecycleEvent(\n    AFTER_INIT_EVENT)
return

|||
Server -> "Service(s)" as Services ++: init()

Services -> Services: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)

Services -> Services ++: initInternal()

Services -> Engine ++: init()
Engine -> Engine: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)
Engine -> Engine: initInternal()
note right
The Engine initializes any nested Hosts,
Contexts and Wrappers as well as any
associated Valves, Loaders, Realms,
Clusters and Managers etc.
end note
Engine -> Engine: fireLifecycleEvent(\n    AFTER_INIT_EVENT)
return

|||
Services -> "Executor(s)" as Executors ++: init()
Executors -> Executors: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)
Executors -> Executors: initInternal()
Executors -> Executors: fireLifecycleEvent(\n    AFTER_INIT_EVENT)
return

|||
Services -> "Mapper\nListener" as MapperListener ++: init()
MapperListener -> MapperListener: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)
MapperListener -> MapperListener: initInternal()
MapperListener -> MapperListener: fireLifecycleEvent(\n    AFTER_INIT_EVENT)
return

|||
Services -> "Connector(s)" as Connectors ++: init()

Connectors -> Connectors: fireLifecycleEvent(\n    BEFORE_INIT_EVENT)

Connectors -> Connectors ++: initInternal()

Connectors -->> CoyoteAdapter **

Connectors -> Protocol ++: setAdapter()
return

Connectors -> Protocol ++: init()
return

return

|||
Connectors -> Connectors: fireLifecycleEvent(\n    AFTER_INIT_EVENT)

return

return

|||
Services -> Services: fireLifecycleEvent(\n    AFTER_INIT_EVENT)

return

return

|||
Server -> Server: fireLifecycleEvent(\n    AFTER_INIT_EVENT)

return
@enduml