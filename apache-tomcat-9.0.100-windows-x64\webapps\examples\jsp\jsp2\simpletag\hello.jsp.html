<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="mytag" uri="/WEB-INF/jsp/jsp2-example-taglib.tld" %>
&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Examples - Hello World SimpleTag Handler&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Examples - Hello World SimpleTag Handler&lt;/h1>
    &lt;hr>
    &lt;p>This tag handler simply echos "Hello, World!"  It's an example of
    a very basic SimpleTag handler with no body.&lt;/p>
    &lt;br>
    &lt;b>&lt;u>Result:&lt;/u>&lt;/b>
    &lt;mytag:helloWorld/>
  &lt;/body>
&lt;/html>
</pre></body></html>