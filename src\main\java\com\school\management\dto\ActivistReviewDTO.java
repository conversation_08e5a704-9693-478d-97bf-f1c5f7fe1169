package com.school.management.dto;

import java.util.Date;

/**
 * 积极分子审议数据传输对象
 */
public class ActivistReviewDTO {
    
    private Integer applicantId;           // 申请人ID
    private Date activistDate;             // 确认入党积极分子时间
    private String branchSecretary;        // 支部书记
    private Boolean hasLeagueRecommendation; // 是否经过共青团推优
    private String remarks;                // 备注
    
    // 构造函数
    public ActivistReviewDTO() {}
    
    public ActivistReviewDTO(Integer applicantId, Date activistDate, String branchSecretary, 
                           Boolean hasLeagueRecommendation, String remarks) {
        this.applicantId = applicantId;
        this.activistDate = activistDate;
        this.branchSecretary = branchSecretary;
        this.hasLeagueRecommendation = hasLeagueRecommendation;
        this.remarks = remarks;
    }
    
    // Getter和Setter方法
    public Integer getApplicantId() { return applicantId; }
    public void setApplicantId(Integer applicantId) { this.applicantId = applicantId; }
    
    public Date getActivistDate() { return activistDate; }
    public void setActivistDate(Date activistDate) { this.activistDate = activistDate; }
    
    public String getBranchSecretary() { return branchSecretary; }
    public void setBranchSecretary(String branchSecretary) { this.branchSecretary = branchSecretary; }
    
    public Boolean getHasLeagueRecommendation() { return hasLeagueRecommendation; }
    public void setHasLeagueRecommendation(Boolean hasLeagueRecommendation) { 
        this.hasLeagueRecommendation = hasLeagueRecommendation; 
    }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    @Override
    public String toString() {
        return "ActivistReviewDTO{" +
                "applicantId=" + applicantId +
                ", activistDate=" + activistDate +
                ", branchSecretary='" + branchSecretary + '\'' +
                ", hasLeagueRecommendation=" + hasLeagueRecommendation +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
