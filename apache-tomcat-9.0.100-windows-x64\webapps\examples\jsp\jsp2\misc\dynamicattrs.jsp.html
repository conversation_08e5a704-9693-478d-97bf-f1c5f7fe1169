<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="my" uri="http://tomcat.apache.org/jsp2-example-taglib"%>
&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Examples - Dynamic Attributes&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Examples - Dynamic Attributes&lt;/h1>
    &lt;hr>
    &lt;p>This JSP page invokes a custom tag that accepts a dynamic set
    of attributes.  The tag echoes the name and value of all attributes
    passed to it.&lt;/p>
    &lt;hr>
    &lt;h2>Invocation 1 (six attributes)&lt;/h2>
    &lt;ul>
      &lt;my:echoAttributes x="1" y="2" z="3" r="red" g="green" b="blue"/>
    &lt;/ul>
    &lt;h2>Invocation 2 (zero attributes)&lt;/h2>
    &lt;ul>
      &lt;my:echoAttributes/>
    &lt;/ul>
    &lt;h2>Invocation 3 (three attributes)&lt;/h2>
    &lt;ul>
      &lt;my:echoAttributes dogName="Scruffy"
                         catName="Fluffy"
                         blowfishName="Puffy"/>
    &lt;/ul>
  &lt;/body>
&lt;/html>
</pre></body></html>