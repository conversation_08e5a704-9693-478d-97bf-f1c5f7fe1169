package com.school.management.test;

import com.school.management.util.DBUtil;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 数据库连接测试类
 * 用于验证数据库连接配置是否正确
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("数据库连接测试");
        System.out.println("========================================");
        
        testDatabaseConnection();
        testDatabaseInfo();
    }
    
    /**
     * 测试数据库连接
     */
    public static void testDatabaseConnection() {
        System.out.println("\n1. 测试数据库连接...");
        
        Connection conn = null;
        try {
            // 获取数据库连接
            conn = DBUtil.getConnection();
            
            if (conn != null && !conn.isClosed()) {
                System.out.println("✅ 数据库连接成功！");
                
                // 获取数据库信息
                String url = conn.getMetaData().getURL();
                String username = conn.getMetaData().getUserName();
                String databaseName = conn.getCatalog();
                
                System.out.println("📊 连接信息：");
                System.out.println("   数据库URL: " + url);
                System.out.println("   用户名: " + username);
                System.out.println("   数据库名: " + databaseName);
                
            } else {
                System.out.println("❌ 数据库连接失败：连接为空或已关闭");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ 数据库连接失败：" + e.getMessage());
            System.out.println("💡 可能的原因：");
            System.out.println("   1. MySQL服务未启动");
            System.out.println("   2. 数据库名称不存在");
            System.out.println("   3. 用户名或密码错误");
            System.out.println("   4. MySQL JDBC驱动未添加到classpath");
            
        } catch (Exception e) {
            System.out.println("❌ 连接测试异常：" + e.getMessage());
            e.printStackTrace();
            
        } finally {
            // 关闭连接
            if (conn != null) {
                try {
                    conn.close();
                    System.out.println("🔒 数据库连接已关闭");
                } catch (SQLException e) {
                    System.out.println("⚠️ 关闭连接时出错：" + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 测试数据库配置信息
     */
    public static void testDatabaseInfo() {
        System.out.println("\n2. 数据库配置信息...");
        
        try {
            // 这里可以读取配置文件信息
            System.out.println("📋 当前配置：");
            System.out.println("   驱动类: com.mysql.cj.jdbc.Driver");
            System.out.println("   连接URL: **************************************");
            System.out.println("   用户名: root");
            System.out.println("   密码: ******");
            
            System.out.println("\n🔧 连接池配置：");
            System.out.println("   初始连接数: 5");
            System.out.println("   最大连接数: 20");
            System.out.println("   最大空闲连接数: 10");
            System.out.println("   最小空闲连接数: 5");
            System.out.println("   最大等待时间: 60000ms");
            
        } catch (Exception e) {
            System.out.println("❌ 读取配置信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试数据库表是否存在
     */
    public static void testTablesExist() {
        System.out.println("\n3. 测试数据库表...");
        
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            
            if (conn != null) {
                // 检查主要表是否存在
                String[] tables = {
                    "party_applicant",
                    "party_activist", 
                    "development_target",
                    "probationary_member",
                    "formal_member",
                    "transfer_letter"
                };
                
                for (String tableName : tables) {
                    try {
                        var stmt = conn.prepareStatement("SELECT COUNT(*) FROM " + tableName);
                        var rs = stmt.executeQuery();
                        if (rs.next()) {
                            int count = rs.getInt(1);
                            System.out.println("✅ 表 " + tableName + " 存在，记录数: " + count);
                        }
                        rs.close();
                        stmt.close();
                    } catch (SQLException e) {
                        System.out.println("❌ 表 " + tableName + " 不存在或查询失败");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试数据库表失败：" + e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 完整测试
     */
    public static boolean runFullTest() {
        System.out.println("========================================");
        System.out.println("完整数据库测试");
        System.out.println("========================================");
        
        boolean allPassed = true;
        
        try {
            // 1. 连接测试
            testDatabaseConnection();
            
            // 2. 配置信息测试
            testDatabaseInfo();
            
            // 3. 表存在性测试
            testTablesExist();
            
            System.out.println("\n========================================");
            System.out.println("测试完成");
            System.out.println("========================================");
            
        } catch (Exception e) {
            System.out.println("❌ 测试过程中出现异常：" + e.getMessage());
            allPassed = false;
        }
        
        return allPassed;
    }
}
