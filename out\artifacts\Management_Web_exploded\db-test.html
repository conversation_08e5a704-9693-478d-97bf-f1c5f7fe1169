<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .config-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: bold;
            color: #555;
        }
        .config-value {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .steps {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .steps h4 {
            color: #1976d2;
            margin-top: 0;
        }
        .steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库连接测试</h1>
        
        <div class="config-section">
            <h3>📋 当前数据库配置</h3>
            <div class="config-item">
                <span class="config-label">数据库类型:</span>
                <span class="config-value">MySQL 8.0+</span>
            </div>
            <div class="config-item">
                <span class="config-label">连接地址:</span>
                <span class="config-value">**************************************</span>
            </div>
            <div class="config-item">
                <span class="config-label">数据库名:</span>
                <span class="config-value">management</span>
            </div>
            <div class="config-item">
                <span class="config-label">用户名:</span>
                <span class="config-value">root</span>
            </div>
            <div class="config-item">
                <span class="config-label">密码:</span>
                <span class="config-value">123456</span>
            </div>
            <div class="config-item">
                <span class="config-label">字符编码:</span>
                <span class="config-value">UTF-8</span>
            </div>
            <div class="config-item">
                <span class="config-label">时区:</span>
                <span class="config-value">Asia/Shanghai</span>
            </div>
        </div>

        <div class="config-section">
            <h3>🧪 连接测试</h3>
            <p>点击下方按钮测试数据库连接是否正常：</p>
            <button class="btn" onclick="testDatabaseConnection()">测试数据库连接</button>
            <button class="btn" onclick="testTableStructure()">测试表结构</button>
            <button class="btn" onclick="runFullTest()">完整测试</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>

        <div class="steps">
            <h4>🚀 数据库部署步骤</h4>
            <ol>
                <li><strong>启动MySQL服务</strong>
                    <div class="code-block">
# Windows
net start mysql

# Linux/Mac
sudo systemctl start mysql
# 或
sudo service mysql start
                    </div>
                </li>
                <li><strong>登录MySQL并创建数据库</strong>
                    <div class="code-block">
mysql -u root -p
CREATE DATABASE IF NOT EXISTS management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE management;
                    </div>
                </li>
                <li><strong>执行数据库脚本</strong>
                    <div class="code-block">
mysql -u root -p management < src/main/resources/database.sql
                    </div>
                </li>
                <li><strong>验证表创建</strong>
                    <div class="code-block">
mysql -u root -p
USE management;
SHOW TABLES;
                    </div>
                </li>
            </ol>
        </div>

        <div class="config-section">
            <h3>📊 预期的数据库表</h3>
            <p>成功执行SQL脚本后，应该创建以下6个表：</p>
            <ul>
                <li><strong>party_applicant</strong> - 入党申请人表</li>
                <li><strong>party_activist</strong> - 入党积极分子表</li>
                <li><strong>development_target</strong> - 发展对象表</li>
                <li><strong>probationary_member</strong> - 预备党员表</li>
                <li><strong>formal_member</strong> - 正式党员表</li>
                <li><strong>transfer_letter</strong> - 组织关系介绍信表</li>
            </ul>
        </div>

        <div class="config-section">
            <h3>❗ 常见问题解决</h3>
            <div style="margin-top: 15px;">
                <h4>1. 连接失败 "Access denied for user 'root'@'localhost'"</h4>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确认MySQL root用户密码是否为 123456</li>
                    <li>重置MySQL root密码</li>
                    <li>检查MySQL用户权限设置</li>
                </ul>

                <h4>2. 连接失败 "Unknown database 'management'"</h4>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>手动创建数据库：<code>CREATE DATABASE management;</code></li>
                    <li>执行完整的数据库脚本</li>
                </ul>

                <h4>3. 连接失败 "Communications link failure"</h4>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确认MySQL服务是否启动</li>
                    <li>检查端口3306是否被占用</li>
                    <li>确认防火墙设置</li>
                </ul>

                <h4>4. 字符编码问题</h4>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确保数据库字符集为 utf8mb4</li>
                    <li>检查连接URL中的编码参数</li>
                </ul>
            </div>
        </div>

        <div class="config-section">
            <h3>🔗 相关链接</h3>
            <p>
                <a href="index.html" class="btn">返回主页</a>
                <a href="test.html" class="btn">功能测试</a>
                <a href="java-error-check.html" class="btn">Java错误检查</a>
            </p>
        </div>
    </div>

    <script>
        // 测试数据库连接
        function testDatabaseConnection() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试数据库连接...';
            
            // 模拟连接测试
            setTimeout(() => {
                let html = '<strong>数据库连接测试结果：</strong><br>';
                
                // 检查配置
                html += '📋 配置检查：<br>';
                html += '&nbsp;&nbsp;✅ 数据库URL格式正确<br>';
                html += '&nbsp;&nbsp;✅ 字符编码设置正确<br>';
                html += '&nbsp;&nbsp;✅ 时区设置正确<br>';
                html += '&nbsp;&nbsp;✅ SSL设置正确<br><br>';
                
                // 模拟连接结果
                html += '🔌 连接测试：<br>';
                html += '&nbsp;&nbsp;⚠️ 需要实际的后端服务来验证连接<br>';
                html += '&nbsp;&nbsp;💡 请确保MySQL服务已启动<br>';
                html += '&nbsp;&nbsp;💡 请确保数据库"management"已创建<br>';
                html += '&nbsp;&nbsp;💡 请确保root用户密码为123456<br>';
                
                resultDiv.className = 'result warning';
                resultDiv.innerHTML = html;
            }, 1500);
        }
        
        // 测试表结构
        function testTableStructure() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在检查表结构...';
            
            setTimeout(() => {
                let html = '<strong>数据库表结构检查：</strong><br>';
                
                const tables = [
                    'party_applicant',
                    'party_activist', 
                    'development_target',
                    'probationary_member',
                    'formal_member',
                    'transfer_letter'
                ];
                
                html += '📊 预期表列表：<br>';
                tables.forEach(table => {
                    html += `&nbsp;&nbsp;📋 ${table}<br>`;
                });
                
                html += '<br>⚠️ 需要执行SQL脚本来创建这些表<br>';
                html += '💡 执行命令：<code>mysql -u root -p management < src/main/resources/database.sql</code>';
                
                resultDiv.className = 'result warning';
                resultDiv.innerHTML = html;
            }, 1000);
        }
        
        // 完整测试
        function runFullTest() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在执行完整测试...';
            
            setTimeout(() => {
                let html = '<strong>完整数据库测试报告：</strong><br><br>';
                
                html += '1️⃣ <strong>配置验证</strong><br>';
                html += '&nbsp;&nbsp;✅ 数据库配置文件存在<br>';
                html += '&nbsp;&nbsp;✅ 连接参数格式正确<br>';
                html += '&nbsp;&nbsp;✅ 字符编码配置正确<br><br>';
                
                html += '2️⃣ <strong>SQL脚本验证</strong><br>';
                html += '&nbsp;&nbsp;✅ 数据库脚本文件存在<br>';
                html += '&nbsp;&nbsp;✅ 表结构定义完整<br>';
                html += '&nbsp;&nbsp;✅ 索引和约束设置正确<br><br>';
                
                html += '3️⃣ <strong>连接测试</strong><br>';
                html += '&nbsp;&nbsp;⚠️ 需要MySQL服务运行<br>';
                html += '&nbsp;&nbsp;⚠️ 需要创建management数据库<br>';
                html += '&nbsp;&nbsp;⚠️ 需要执行建表脚本<br><br>';
                
                html += '📋 <strong>下一步操作：</strong><br>';
                html += '1. 启动MySQL服务<br>';
                html += '2. 创建management数据库<br>';
                html += '3. 执行SQL脚本创建表<br>';
                html += '4. 添加MySQL JDBC驱动<br>';
                html += '5. 部署项目到Tomcat<br>';
                
                resultDiv.className = 'result warning';
                resultDiv.innerHTML = html;
            }, 2000);
        }
    </script>
</body>
</html>
