<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Expression Language - Basic Arithmetic&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Expression Language - Basic Arithmetic&lt;/h1>
    &lt;hr>
    This example illustrates basic Expression Language arithmetic.
    Addition (+), subtraction (-), multiplication (*), division (/ or div),
    and modulus (% or mod) are all supported.  Error conditions, like
    division by zero, are handled gracefully.
    &lt;br>
    &lt;blockquote>
      &lt;code>
        &lt;table border="1">
          &lt;thead>
        &lt;td>&lt;b>EL Expression&lt;/b>&lt;/td>
        &lt;td>&lt;b>Result&lt;/b>&lt;/td>
      &lt;/thead>
      &lt;tr>
        &lt;td>\${1}&lt;/td>
        &lt;td>${1}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1 + 2}&lt;/td>
        &lt;td>${1 + 2}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1.2 + 2.3}&lt;/td>
        &lt;td>${1.2 + 2.3}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${1.2E4 + 1.4}&lt;/td>
        &lt;td>${1.2E4 + 1.4}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${-4 - 2}&lt;/td>
        &lt;td>${-4 - 2}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${21 * 2}&lt;/td>
        &lt;td>${21 * 2}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${3/4}&lt;/td>
        &lt;td>${3/4}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${3 div 4}&lt;/td>
        &lt;td>${3 div 4}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${3/0}&lt;/td>
        &lt;td>${3/0}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${10%4}&lt;/td>
        &lt;td>${10%4}&lt;/td>
      &lt;/tr>
      &lt;tr>
        &lt;td>\${10 mod 4}&lt;/td>
        &lt;td>${10 mod 4}&lt;/td>
      &lt;/tr>
    &lt;tr>
      &lt;td>\${(1==2) ? 3 : 4}&lt;/td>
      &lt;td>${(1==2) ? 3 : 4}&lt;/td>
    &lt;/tr>
    &lt;/table>
      &lt;/code>
    &lt;/blockquote>
  &lt;/body>
&lt;/html>
</pre></body></html>