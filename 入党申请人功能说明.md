# 入党申请人功能说明

## 功能概述

入党申请人管理模块是学校党员信息管理系统的核心功能之一，提供完整的申请人信息维护和积极分子审议功能。

## 📊 功能特点

### 1. 维护功能
- **新增申请人**: 录入新的入党申请人信息
- **修改申请人**: 编辑已有申请人的信息
- **删除申请人**: 删除不需要的申请人记录
- **查询申请人**: 支持多条件搜索和分页显示

### 2. 业务功能
- **积极分子审议**: 将符合条件的申请人转为入党积极分子
- **状态管理**: 自动管理申请人的状态流转
- **信息传递**: 申请人信息自动传递到积极分子阶段

## 🔍 申请人信息字段

### 基本信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 姓名 | 文本 | ✅ | 申请人真实姓名 |
| 身份证号 | 文本 | ✅ | 18位身份证号码 |
| 性别 | 文本 | 自动 | 从身份证号自动识别 |
| 出生日期 | 日期 | 自动 | 从身份证号自动识别 |
| 年龄 | 数字 | 自动 | 根据出生日期自动计算 |
| 户籍地 | 文本 | ❌ | 户口所在地 |
| 地址 | 文本 | ❌ | 现居住地址 |
| 联系电话 | 文本 | ❌ | 手机号码 |
| 年级 | 选择 | ✅ | 学生所在年级 |
| 是否为共青团员 | 布尔 | ❌ | 默认为否 |
| 申请日期 | 日期 | ❌ | 提交入党申请的日期 |
| 状态 | 枚举 | 自动 | 待审核/已通过/已拒绝/已转积极分子 |
| 备注 | 文本 | ❌ | 其他说明信息 |

### 自动识别功能
系统支持从18位身份证号自动识别以下信息：
- **性别**: 根据身份证第17位数字判断（奇数为男，偶数为女）
- **出生日期**: 从身份证第7-14位提取年月日
- **年龄**: 根据出生日期和当前日期计算

## 🔄 积极分子审议功能

### 审议条件
申请人必须满足以下条件才能进行积极分子审议：
1. **状态为"已通过"**: 申请已经通过初步审核
2. **信息完整**: 姓名、身份证号等关键信息不能为空
3. **未重复转换**: 该申请人尚未转为积极分子

### 审议信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 确认入党积极分子时间 | 日期 | ✅ | 正式确认为积极分子的日期 |
| 支部书记 | 文本 | ✅ | 负责审议的支部书记姓名 |
| 是否经过共青团推优 | 布尔 | ❌ | 是否通过共青团组织推荐 |
| 审议备注 | 文本 | ❌ | 审议过程的说明和备注 |

### 转换流程
1. **选择申请人**: 从状态为"已通过"的申请人中选择
2. **填写审议信息**: 录入确认时间、支部书记等信息
3. **确认转换**: 点击"确定转为积极分子"按钮
4. **自动处理**: 系统自动完成以下操作：
   - 创建新的积极分子记录
   - 复制申请人的基本信息
   - 更新申请人状态为"已转积极分子"
   - 记录审议相关信息

## 🖥️ 界面功能

### 主界面
- **工具栏**: 新增、刷新、导出等操作按钮
- **搜索栏**: 支持按姓名、身份证号、年级、状态搜索
- **数据表格**: 分页显示申请人列表
- **操作列**: 编辑、删除、积极分子审议等操作

### 新增/编辑模态框
- **表单验证**: 必填字段验证和格式检查
- **自动填充**: 身份证号输入后自动显示性别、年龄等信息
- **友好提示**: 实时显示解析的身份证信息

### 积极分子审议模态框
- **申请人信息展示**: 显示待审议申请人的详细信息
- **审议表单**: 录入审议相关信息
- **确认机制**: 二次确认防止误操作

## 🔧 技术实现

### 前端技术
- **JSP页面**: 服务器端渲染，支持动态内容
- **JavaScript**: 原生JS实现交互逻辑
- **CSS3**: 响应式设计和美观界面
- **AJAX**: 异步数据交互

### 后端技术
- **Servlet**: 处理HTTP请求和响应
- **Service层**: 业务逻辑处理
- **DAO层**: 数据访问操作
- **DTO**: 数据传输对象

### 数据库设计
```sql
-- 入党申请人表
CREATE TABLE party_applicant (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男', '女'),
    birth_date DATE,
    age INT,
    id_card VARCHAR(18) UNIQUE NOT NULL,
    native_place VARCHAR(100),
    address VARCHAR(200),
    phone VARCHAR(20),
    grade VARCHAR(20),
    is_league_member BOOLEAN DEFAULT FALSE,
    application_date DATE,
    status ENUM('待审核', '已通过', '已拒绝', '已转积极分子') DEFAULT '待审核',
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 入党积极分子表
CREATE TABLE party_activist (
    id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_id INT,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男', '女'),
    birth_date DATE,
    age INT,
    id_card VARCHAR(18),
    native_place VARCHAR(100),
    address VARCHAR(200),
    phone VARCHAR(20),
    grade VARCHAR(20),
    is_league_member BOOLEAN,
    application_date DATE,
    activist_date DATE,
    branch_secretary VARCHAR(50),
    has_league_recommendation BOOLEAN,
    status VARCHAR(20) DEFAULT '积极分子',
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (applicant_id) REFERENCES party_applicant(id)
);
```

## 📋 API接口

### 申请人管理接口
- `GET /api/applicant/list` - 获取申请人列表（支持分页和搜索）
- `GET /api/applicant/{id}` - 获取指定申请人详情
- `POST /api/applicant/add` - 新增申请人
- `PUT /api/applicant/{id}` - 更新申请人信息
- `DELETE /api/applicant/{id}` - 删除申请人

### 积极分子审议接口
- `POST /api/applicant/review` - 提交积极分子审议

## 🧪 使用说明

### 1. 新增申请人
1. 点击"新增申请人"按钮
2. 填写申请人基本信息
3. 输入身份证号后系统自动识别性别、年龄等信息
4. 点击"保存"完成新增

### 2. 编辑申请人
1. 在申请人列表中点击"编辑"按钮
2. 修改需要更新的信息
3. 点击"保存"完成修改

### 3. 积极分子审议
1. 确保申请人状态为"已通过"
2. 点击申请人操作列中的"积极分子审议"按钮
3. 查看申请人信息确认无误
4. 填写审议信息（确认时间、支部书记等）
5. 点击"确定转为积极分子"完成转换

### 4. 搜索和筛选
1. 在搜索栏输入搜索条件
2. 支持按姓名、身份证号、年级、状态搜索
3. 点击"搜索"按钮执行搜索
4. 点击"重置"按钮清空搜索条件

## ⚠️ 注意事项

### 数据安全
- 身份证号码为敏感信息，需要严格保护
- 建议在生产环境中对身份证号进行脱敏显示
- 定期备份重要数据

### 操作规范
- 积极分子审议是不可逆操作，请谨慎操作
- 删除申请人前请确认该记录不再需要
- 建议定期审核申请人状态，及时处理

### 系统维护
- 定期清理无效数据
- 监控系统性能和响应时间
- 及时更新年级选项等基础数据

## 🔗 相关功能

- **入党积极分子管理**: 管理已转换的积极分子信息
- **发展对象管理**: 积极分子的下一阶段管理
- **数据统计**: 各阶段人员统计和报表
- **权限管理**: 控制不同用户的操作权限

---

**文档版本**: v1.0  
**最后更新**: 2025年8月8日  
**适用系统**: 学校党员信息管理系统
