<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.100) - The AJP Connector</title><meta name="author" content="Yoav Shapira"><meta name="author" content="Andrew R. Jaquith"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The AJP Connector</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementations">Standard Implementations</a></li><li><a href="#Java_TCP_socket_attributes">Java TCP socket attributes</a></li><li><a href="#NIO_specific_configuration">NIO specific configuration</a></li><li><a href="#NIO2_specific_configuration">NIO2 specific configuration</a></li><li><a href="#APR/native_specific_configuration">APR/native specific configuration</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a><ol><li><a href="#Proxy_Support">Proxy Support</a></li><li><a href="#Connector_Comparison">Connector Comparison</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>AJP Connector</strong> element represents a
  <strong>Connector</strong> component that communicates with a web
  connector via the <code>AJP</code> protocol.  This is used for cases
  where you wish to invisibly integrate Tomcat into an existing (or new)
  Apache installation, and you want Apache to handle the static content
  contained in the web application, and/or utilize Apache's SSL
  processing.</p>

  <p>Use of the AJP protocol requires additional security considerations because
  it allows greater direct manipulation of Tomcat's internal data structures
  than the HTTP connectors. Particular attention should be paid to the values
  used for the <code>address</code>, <code>secret</code>,
  <code>secretRequired</code> and <code>allowedRequestAttributesPattern</code>
  attributes.</p>

  <p>This connector supports load balancing when used in conjunction with
  the <code>jvmRoute</code> attribute of the
  <a href="engine.html">Engine</a>.</p>

  <p>The native connectors supported with this Tomcat release are:</p>
    <ul>
      <li>JK 1.2.x with any of the supported servers. See
      <a href="https://tomcat.apache.org/connectors-doc/">the JK docs</a>
      for details.</li>
      <li>mod_proxy on Apache httpd 2.x (included by default in Apache HTTP
      Server 2.2), with AJP enabled: see
      <a href="https://httpd.apache.org/docs/2.2/mod/mod_proxy_ajp.html">the
      httpd docs</a> for details.</li>
    </ul>

  <p><b>Other native connectors supporting AJP may work, but are no longer
  supported.</b></p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

  <p>All implementations of <strong>Connector</strong>
  support the following attributes:</p>

  <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_allowTrace"><td><code class="attributeName">allowTrace</code></td><td>
      <p>A boolean value which can be used to enable or disable the TRACE
      HTTP method. If not specified, this attribute is set to false. As per RFC
      7231 section 4.3.8, cookie and authorization headers will be excluded from
      the response to the TRACE request. If you wish to include these, you can
      implement the <code>doTrace()</code> method for the target Servlet and
      gain full control over the response.</p>
    </td></tr><tr id="Attributes_Common Attributes_asyncTimeout"><td><code class="attributeName">asyncTimeout</code></td><td>
      <p>The default timeout for asynchronous requests in milliseconds. If not
      specified, this attribute is set to the Servlet specification default of
      30000 (30 seconds).</p>
    </td></tr><tr id="Attributes_Common Attributes_discardFacades"><td><code class="attributeName">discardFacades</code></td><td>
      <p>A boolean value which can be used to enable or disable the recycling
      of the facade objects that isolate the container internal request
      processing objects. If set to <code>true</code> the facades will be
      set for garbage collection after every request, otherwise they will be
      reused. This setting has no effect when the security manager is enabled.
      If not specified, this attribute is set to <code>true</code>.</p>
    </td></tr><tr id="Attributes_Common Attributes_enableLookups"><td><code class="attributeName">enableLookups</code></td><td>
      <p>Set to <code>true</code> if you want calls to
      <code>request.getRemoteHost()</code> to perform DNS lookups in
      order to return the actual host name of the remote client.  Set
      to <code>false</code> to skip the DNS lookup and return the IP
      address in String form instead (thereby improving performance).
      By default, DNS lookups are disabled.</p>
    </td></tr><tr id="Attributes_Common Attributes_encodedReverseSolidusHandling"><td><code class="attributeName">encodedReverseSolidusHandling</code></td><td>
      <p>When set to <code>reject</code> request paths containing a
      <code>%5c</code> sequence will be rejected with a 400 response. When set
      to <code>decode</code> request paths containing a <code>%5c</code>
      sequence will have that sequence decoded to <code>\</code> at the same
      time other <code>%nn</code> sequences are decoded. When set to
      <code>passthrough</code> request paths containing a <code>%5c</code>
      sequence will be processed with the <code>%5c</code> sequence unchanged.
      </p>
      <p>When set to <code>decoded</code>, the <strong>allowBackslash</strong>
      attribute will be applied after decoding.
      </p>
      <p>If <code>passthrough</code> is used then it is the application's
      resposibility to perform any further <code>%nn</code> decoding required.
      Any <code>%25</code> sequences (encoded <code>%</code>) in the request
      path with also be processed with the <code>%25</code> sequence unchanged
      to avoid potential corruption and/or decoding failure when the path is
      subsequently <code>%nn</code> decoded by the application.</p>
      <p>If not specified, the default value is <code>decode</code>.</p>
    </td></tr><tr id="Attributes_Common Attributes_encodedSolidusHandling"><td><code class="attributeName">encodedSolidusHandling</code></td><td>
      <p>When set to <code>reject</code> request paths containing a
      <code>%2f</code> sequence will be rejected with a 400 response. When set
      to <code>decode</code> request paths containing a <code>%2f</code>
      sequence will have that sequence decoded to <code>/</code> at the same
      time other <code>%nn</code> sequences are decoded. When set to
      <code>passthrough</code> request paths containing a <code>%2f</code>
      sequence will be processed with the <code>%2f</code> sequence unchanged.
      </p>
      <p>If <code>passthrough</code> is used then it is the application's
      resposibility to perform any further <code>%nn</code> decoding required.
      Any <code>%25</code> sequences (encoded <code>%</code>) in the request
      path with also be processed with the <code>%25</code> sequence unchanged
      to avoid potential corruption and/or decoding failure when the path is
      subsequently <code>%nn</code> decoded by the application.</p>
      <p>If not specified the default value is <code>reject</code>. This default
      may be modified if the deprecated <a href="systemprops.html">system
      property</a>
      <code>org.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH</code> is
      set.</p>
    </td></tr><tr id="Attributes_Common Attributes_maxCookieCount"><td><code class="attributeName">maxCookieCount</code></td><td>
      <p>The maximum number of cookies that are permitted for a request. A value
      of less than zero means no limit. If not specified, a default value of 200
      will be used.</p>
    </td></tr><tr id="Attributes_Common Attributes_maxParameterCount"><td><code class="attributeName">maxParameterCount</code></td><td>
      <p>The maximum total number of request parameters (including uploaded
      files) obtained from the query string and, for POST requests, the request
      body if the content type is
      <code>application/x-www-form-urlencoded</code> or
      <code>multipart/form-data</code>. Request parameters beyond this limit
      will be ignored. A value of less than 0 means no limit. If not specified,
      a default of 10000 is used. Note that <code>FailedRequestFilter</code>
      <a href="filter.html">filter</a> can be used to reject requests that
      exceed the limit.</p>
    </td></tr><tr id="Attributes_Common Attributes_maxPostSize"><td><code class="attributeName">maxPostSize</code></td><td>
      <p>The maximum size in bytes of the POST which will be handled by
      the container FORM URL parameter parsing. The limit can be disabled by
      setting this attribute to a value less than zero. If not specified, this
      attribute is set to 2097152 (2 MiB). Note that the
      <a href="filter.html#Failed_Request_Filter"><code>FailedRequestFilter</code></a>
      can be used to reject requests that exceed this limit.</p>
    </td></tr><tr id="Attributes_Common Attributes_maxSavePostSize"><td><code class="attributeName">maxSavePostSize</code></td><td>
      <p>The maximum size in bytes of the POST which will be saved/buffered by
      the container during FORM or CLIENT-CERT authentication. For both types
      of authentication, the POST will be saved/buffered before the user is
      authenticated. For CLIENT-CERT authentication, the POST is buffered for
      the duration of the SSL handshake and the buffer emptied when the request
      is processed. For FORM authentication the POST is saved whilst the user
      is re-directed to the login form and is retained until the user
      successfully authenticates or the session associated with the
      authentication request expires. The limit can be disabled by setting this
      attribute to -1. Setting the attribute to zero will disable the saving of
      POST data during authentication. If not specified, this attribute is set
      to 4096 (4 KiB).</p>
    </td></tr><tr id="Attributes_Common Attributes_parseBodyMethods"><td><code class="attributeName">parseBodyMethods</code></td><td>
      <p>A comma-separated list of HTTP methods for which request
      bodies using <code>application/x-www-form-urlencoded</code> will be parsed
      for request parameters identically to POST. This is useful in RESTful
      applications that want to support POST-style semantics for PUT requests.
      Note that any setting other than <code>POST</code> causes Tomcat
      to behave in a way that goes against the intent of the servlet
      specification.
      The HTTP method TRACE is specifically forbidden here in accordance
      with the HTTP specification.
      The default is <code>POST</code></p>
    </td></tr><tr id="Attributes_Common Attributes_port"><td><strong><code class="attributeName">port</code></strong></td><td>
      <p>The TCP port number on which this <strong>Connector</strong>
      will create a server socket and await incoming connections.  Your
      operating system will allow only one server application to listen
      to a particular port number on a particular IP address. If the special
      value of 0 (zero) is used, then Tomcat will select a free port at random
      to use for this connector. This is typically only useful in embedded and
      testing applications.</p>
    </td></tr><tr id="Attributes_Common Attributes_protocol"><td><code class="attributeName">protocol</code></td><td>
      <p>Sets the protocol to handle incoming traffic. To configure an AJP
        connector this must be specified. If no value for protocol is provided,
        an <a href="http.html">HTTP connector</a> rather than an AJP connector
        will be configured.<br>
        The standard protocol value for an AJP connector is <code>AJP/1.3</code>
        which uses an auto-switching mechanism to select either a Java NIO based
        connector or an APR/native based connector. If the
        <code>PATH</code> (Windows) or <code>LD_LIBRARY_PATH</code> (on most unix
        systems) environment variables contain the Tomcat native library, the
        native/APR connector will be used. If the native library cannot be
        found, the Java NIO based connector will be used.<br>
        To use an explicit protocol rather than rely on the auto-switching
        mechanism described above, the following values may be used:<br>
        <code>org.apache.coyote.ajp.AjpNioProtocol</code>
        - non blocking Java NIO connector.<br>
        <code>org.apache.coyote.ajp.AjpNio2Protocol</code>
        - non blocking Java NIO2 connector.<br>
        <code>org.apache.coyote.ajp.AjpAprProtocol</code>
        - the APR/native connector.<br>
        Custom implementations may also be used.<br>
        Take a look at our <a href="#Connector_Comparison">Connector
        Comparison</a> chart.
      </p>
    </td></tr><tr id="Attributes_Common Attributes_proxyName"><td><code class="attributeName">proxyName</code></td><td>
      <p>If this <strong>Connector</strong> is being used in a proxy
      configuration, configure this attribute to specify the server name
      to be returned for calls to <code>request.getServerName()</code>.
      See <a href="#Proxy_Support">Proxy Support</a> for more
      information.</p>
    </td></tr><tr id="Attributes_Common Attributes_proxyPort"><td><code class="attributeName">proxyPort</code></td><td>
      <p>If this <strong>Connector</strong> is being used in a proxy
      configuration, configure this attribute to specify the server port
      to be returned for calls to <code>request.getServerPort()</code>.
      See <a href="#Proxy_Support">Proxy Support</a> for more
      information.</p>
    </td></tr><tr id="Attributes_Common Attributes_redirectPort"><td><code class="attributeName">redirectPort</code></td><td>
      <p>If this <strong>Connector</strong> is supporting non-SSL
      requests, and a request is received for which a matching
      <code>&lt;security-constraint&gt;</code> requires SSL transport,
      Catalina will automatically redirect the request to the port
      number specified here.</p>
    </td></tr><tr id="Attributes_Common Attributes_scheme"><td><code class="attributeName">scheme</code></td><td>
      <p>Set this attribute to the name of the protocol you wish to have
      returned by calls to <code>request.getScheme()</code>.  For
      example, you would set this attribute to "<code>https</code>"
      for an SSL Connector.  The default value is "<code>http</code>".
      </p>
    </td></tr><tr id="Attributes_Common Attributes_secure"><td><code class="attributeName">secure</code></td><td>
      <p>Set this attribute to <code>true</code> if you wish to have
      calls to <code>request.isSecure()</code> to return <code>true</code>
      for requests received by this Connector. You would want this on an
      SSL Connector or a non SSL connector that is receiving data from a
      SSL accelerator, like a crypto card, an SSL appliance or even a webserver.
      The default value is <code>false</code>.</p>
    </td></tr><tr id="Attributes_Common Attributes_URIEncoding"><td><code class="attributeName">URIEncoding</code></td><td>
      <p>This specifies the character encoding used to decode the URI bytes,
      after %xx decoding the URL. The default value is <code>UTF-8</code>.</p>
    </td></tr><tr id="Attributes_Common Attributes_useBodyEncodingForURI"><td><code class="attributeName">useBodyEncodingForURI</code></td><td>
      <p>This specifies if the encoding specified in contentType should be used
      for URI query parameters, instead of using the URIEncoding. This
      setting is present for compatibility with Tomcat 4.1.x, where the
      encoding specified in the contentType, or explicitly set using
      Request.setCharacterEncoding method was also used for the parameters from
      the URL. The default value is <code>false</code>.
      </p>
      <p><em>Notes:</em> See notes on this attribute in
      <a href="http.html">HTTP Connector</a> documentation.</p>
    </td></tr><tr id="Attributes_Common Attributes_useIPVHosts"><td><code class="attributeName">useIPVHosts</code></td><td>
      <p>Set this attribute to <code>true</code> to cause Tomcat to use
      the IP address passed by the native web server to determine the Host
      to send the request to.  The default value is <code>false</code>.</p>
    </td></tr><tr id="Attributes_Common Attributes_xpoweredBy"><td><code class="attributeName">xpoweredBy</code></td><td>
      <p>Set this attribute to <code>true</code> to cause Tomcat to advertise
      support for the Servlet specification using the header recommended in the
      specification.  The default value is <code>false</code>.</p>
    </td></tr></table>

  </div></div>

  <div class="subsection"><h4 id="Standard_Implementations">Standard Implementations</h4><div class="text">

  <p>To use AJP, you must specify the protocol attribute (see above).</p>

  <p>The standard AJP connectors (NIO, NIO2 and APR/native) all support the
  following attributes in addition to the common Connector attributes listed
  above.</p>

  <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementations_acceptCount"><td><code class="attributeName">acceptCount</code></td><td>
      <p>The maximum length of the operating system provided queue for incoming
      connection requests when <code>maxConnections</code> has been reached. The
      operating system may ignore this setting and use a different size for the
      queue. When this queue is full, the operating system may actively refuse
      additional connections or those connections may time out. The default
      value is 100.</p>
    </td></tr><tr id="Attributes_Standard Implementations_acceptorThreadCount"><td><code class="attributeName">acceptorThreadCount</code></td><td>
      <p>The number of threads to be used to accept connections. Increase this
      value on a multi CPU machine, although you would never really need more
      than <code>2</code>. Also, with a lot of non keep alive connections, you
      might want to increase this value as well. Default value is
      <code>1</code>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_acceptorThreadPriority"><td><code class="attributeName">acceptorThreadPriority</code></td><td>
      <p>The priority of the acceptor threads. The threads used to accept
      new connections. The default value is <code>5</code> (the value of the
      <code>java.lang.Thread.NORM_PRIORITY</code> constant). See the JavaDoc
      for the <code>java.lang.Thread</code> class for more details on what
      this priority means.</p>
    </td></tr><tr id="Attributes_Standard Implementations_address"><td><code class="attributeName">address</code></td><td>
      <p>For servers with more than one IP address, this attribute specifies
      which address will be used for listening on the specified port. By
      default, the connector will listen on the loopback address. Unless the JVM
      is configured otherwise using system properties, the Java based connectors
      (NIO, NIO2) will listen on both IPv4 and IPv6 addresses when configured
      with either <code>0.0.0.0</code> or <code>::</code>. The APR/native
      connector will only listen on IPv4 addresses if configured with
      <code>0.0.0.0</code> and will listen on IPv6 addresses (and optionally
      IPv4 addresses depending on the setting of <strong>ipv6v6only</strong>) if
      configured with <code>::</code>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_ajpFlush"><td><code class="attributeName">ajpFlush</code></td><td>
      <p>A boolean value which can be used to enable or disable sending
      AJP flush messages to the fronting proxy whenever an explicit
      flush happens. The default value is <code>true</code>.<br>
      An AJP flush message is a SEND_BODY_CHUNK packet with no body content.
      Proxy implementations like mod_jk or mod_proxy_ajp will flush the
      data buffered in the web server to the client when they receive
      such a packet. Setting this to <code>false</code> can reduce
      AJP packet traffic but might delay sending packets to the client.
      At the end of the response, AJP does always flush to the client.</p>
    </td></tr><tr id="Attributes_Standard Implementations_allowedRequestAttributesPattern"><td><code class="attributeName">allowedRequestAttributesPattern</code></td><td>
      <p>The AJP protocol passes some information from the reverse proxy to the
      AJP connector using request attributes. These attributes are:</p>
      <ul>
        <li>javax.servlet.request.cipher_suite</li>
        <li>javax.servlet.request.key_size</li>
        <li>javax.servlet.request.ssl_session</li>
        <li>javax.servlet.request.X509Certificate</li>
        <li>AJP_LOCAL_ADDR</li>
        <li>AJP_REMOTE_PORT</li>
        <li>AJP_SSL_PROTOCOL</li>
        <li>JK_LB_ACTIVATION</li>
        <li>CERT_ISSUER (IIS only)</li>
        <li>CERT_SUBJECT (IIS only)</li>
        <li>CERT_COOKIE (IIS only)</li>
        <li>HTTPS_SERVER_SUBJECT (IIS only)</li>
        <li>CERT_FLAGS (IIS only)</li>
        <li>HTTPS_SECRETKEYSIZE (IIS only)</li>
        <li>CERT_SERIALNUMBER (IIS only)</li>
        <li>HTTPS_SERVER_ISSUER (IIS only)</li>
        <li>HTTPS_KEYSIZE (IIS only)</li>
      </ul>
      <p>The AJP protocol supports the passing of arbitrary request attributes.
      Requests containing arbitrary request attributes will be rejected with a
      403 response unless the entire attribute name matches this regular
      expression. If not specified, the default value is <code>null</code>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_bindOnInit"><td><code class="attributeName">bindOnInit</code></td><td>
      <p>Controls when the socket used by the connector is bound. If set to
      <code>true</code> it is bound when the connector is initiated and unbound
      when the connector is destroyed. If set to <code>false</code>, the socket
      will be bound when the connector is started and unbound when it is
      stopped. If not specified, the default is <code>true</code>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_clientCertProvider"><td><code class="attributeName">clientCertProvider</code></td><td>
      <p>When client certificate information is presented in a form other than
      instances of <code>java.security.cert.X509Certificate</code> it needs to
      be converted before it can be used and this property controls which JSSE
      provider is used to perform the conversion. For example it is used with
      the AJP connectors, the <a href="http.html">HTTP APR connector</a> and
      with the <a href="valve.html#SSL_Authenticator_Valve">
      org.apache.catalina.valves.SSLValve</a>.If not specified, the default
      provider will be used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_connectionLinger"><td><code class="attributeName">connectionLinger</code></td><td>
      <p>The number of seconds during which the sockets used by this
      <strong>Connector</strong> will linger when they are closed. The default
      value is <code>-1</code> which disables socket linger.</p>
    </td></tr><tr id="Attributes_Standard Implementations_connectionTimeout"><td><code class="attributeName">connectionTimeout</code></td><td>
      <p>The number of milliseconds this <strong>Connector</strong> will wait,
      after accepting a connection, for the request URI line to be
      presented.  The default value for AJP protocol connectors
      is <code>-1</code> (i.e. infinite).</p>
    </td></tr><tr id="Attributes_Standard Implementations_executor"><td><code class="attributeName">executor</code></td><td>
      <p>A reference to the name in an <a href="executor.html">Executor</a>
      element. If this attribute is set, and the named executor exists, the
      connector will use the executor, and all the other thread attributes will
      be ignored. Note that if a shared executor is not specified for a
      connector then the connector will use a private, internal executor to
      provide the thread pool.</p>
    </td></tr><tr id="Attributes_Standard Implementations_executorTerminationTimeoutMillis"><td><code class="attributeName">executorTerminationTimeoutMillis</code></td><td>
      <p>The time that the private internal executor will wait for request
      processing threads to terminate before continuing with the process of
      stopping the connector. If not set, the default is <code>5000</code> (5
      seconds).</p>
    </td></tr><tr id="Attributes_Standard Implementations_keepAliveTimeout"><td><code class="attributeName">keepAliveTimeout</code></td><td>
      <p>The number of milliseconds this <strong>Connector</strong> will wait for
       another AJP request before closing the connection.
       The default value is to use the value that has been set for the
       connectionTimeout attribute.</p>
    </td></tr><tr id="Attributes_Standard Implementations_maxConnections"><td><code class="attributeName">maxConnections</code></td><td>
      <p>The maximum number of connections that the server will accept and
      process at any given time. When this number has been reached, the server
      will accept, but not process, one further connection. This additional
      connection be blocked until the number of connections being processed
      falls below <strong>maxConnections</strong> at which point the server will
      start accepting and processing new connections again. Note that once the
      limit has been reached, the operating system may still accept connections
      based on the <code>acceptCount</code> setting. The default value
      is <code>8192</code>.</p>
      <p>For NIO/NIO2 only, setting the value to -1, will disable the
      maxConnections feature and connections will not be counted.</p>
    </td></tr><tr id="Attributes_Standard Implementations_maxHeaderCount"><td><code class="attributeName">maxHeaderCount</code></td><td>
      <p>The maximum number of headers in a request that are allowed by the
      container. A request that contains more headers than the specified limit
      will be rejected. A value of less than 0 means no limit.
      If not specified, a default of 100 is used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_maxThreads"><td><code class="attributeName">maxThreads</code></td><td>
      <p>The maximum number of request processing threads to be created
      by this <strong>Connector</strong>, which therefore determines the
      maximum number of simultaneous requests that can be handled.  If
      not specified, this attribute is set to 200. If an executor is associated
      with this connector, this attribute is ignored as the connector will
      execute tasks using the executor rather than an internal thread pool. Note
      that if an executor is configured any value set for this attribute will be
      recorded correctly but it will be reported (e.g. via JMX) as
      <code>-1</code> to make clear that it is not used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_minSpareThreads"><td><code class="attributeName">minSpareThreads</code></td><td>
      <p>The minimum number of threads always kept running. This includes both
      active and idle threads. If not specified, the default of <code>10</code>
      is used. If an executor is associated with this connector, this attribute
      is ignored as the connector will execute tasks using the executor rather
      than an internal thread pool. Note that if an executor is configured any
      value set for this attribute will be recorded correctly but it will be
      reported (e.g. via JMX) as <code>-1</code> to make clear that it is not
      used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_packetSize"><td><code class="attributeName">packetSize</code></td><td>
      <p>This attribute sets the maximum AJP packet size in Bytes. The maximum
      value is 65536. It should be the same as the <code>max_packet_size</code>
      directive configured for mod_jk. Normally it is not necessary to change
      the maximum packet size. Problems with the default value have been
      reported when sending certificates or certificate chains. The default
      value is 8192. If set to less than 8192 then the setting will ignored and
      the default value of 8192 used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_processorCache"><td><code class="attributeName">processorCache</code></td><td>
      <p>The protocol handler caches Processor objects to speed up performance.
      This setting dictates how many of these objects get cached.
      <code>-1</code> means unlimited, default is <code>200</code>. If not using
      Servlet 3.0 asynchronous processing, a good default is to use the same as
      the maxThreads setting. If using Servlet 3.0 asynchronous processing, a
      good default is to use the larger of maxThreads and the maximum number of
      expected concurrent requests (synchronous and asynchronous).</p>
    </td></tr><tr id="Attributes_Standard Implementations_secret"><td><code class="attributeName">secret</code></td><td>
      <p>Only requests from workers with this secret keyword will be accepted.
      The default value is <code>null</code>. This attribute must be specified
      with a non-null, non-zero length value unless
      <strong>secretRequired</strong> is explicitly configured to be
      <code>false</code>. If this attribute is configured with a non-null,
      non-zero length value then the workers <strong>must</strong> provide a
      matching value else the request will be rejected irrespective of the
      setting of <strong>secretRequired</strong>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_secretRequired"><td><code class="attributeName">secretRequired</code></td><td>
      <p>If this attribute is <code>true</code>, the AJP Connector will only
      start if the <strong>secret</strong> attribute is configured with a
      non-null, non-zero length value. This attribute only controls whether
      the <strong>secret</strong> attribute is required to be specified for the
      AJP Connector to start. It <strong>does not</strong> control whether
      workers are required to provide the secret. The default value is
      <code>true</code>. This attribute should only be set to <code>false</code>
      when the Connector is used on a trusted network.</p>
    </td></tr><tr id="Attributes_Standard Implementations_tcpNoDelay"><td><code class="attributeName">tcpNoDelay</code></td><td>
      <p>If set to <code>true</code>, the TCP_NO_DELAY option will be
      set on the server socket, which improves performance under most
      circumstances.  This is set to <code>true</code> by default.</p>
    </td></tr><tr id="Attributes_Standard Implementations_threadPriority"><td><code class="attributeName">threadPriority</code></td><td>
      <p>The priority of the request processing threads within the JVM.
      The default value is <code>5</code> (the value of the
      <code>java.lang.Thread.NORM_PRIORITY</code> constant). See the JavaDoc
      for the <code>java.lang.Thread</code> class for more details on what
      this priority means.If an executor is associated
      with this connector, this attribute is ignored as the connector will
      execute tasks using the executor rather than an internal thread pool. Note
      that if an executor is configured any value set for this attribute will be
      recorded correctly but it will be reported (e.g. via JMX) as
      <code>-1</code> to make clear that it is not used.</p>
    </td></tr><tr id="Attributes_Standard Implementations_throwOnFailure"><td><code class="attributeName">throwOnFailure</code></td><td>
      <p>If the Connector experiences an Exception during a Lifecycle transition
      should the Exception be rethrown or logged? If not specified, the default
      of <code>false</code> will be used. Note that the default can be changed
      by the <code>org.apache.catalina.startup.EXIT_ON_INIT_FAILURE</code>
      system property.</p>
    </td></tr><tr id="Attributes_Standard Implementations_tomcatAuthentication"><td><code class="attributeName">tomcatAuthentication</code></td><td>
      <p>If set to <code>true</code>, the authentication will be done in Tomcat.
      Otherwise, the authenticated principal will be propagated from the native
      webserver and used for authorization in Tomcat. </p>
      <p>The web server must send the user principal (username) as a request
      <i>attribute</i> named <code>REMOTE_USER</code>.</p>
      <p>Note that this principal will have no roles associated with it.</p>
      <p>The default value is <code>true</code>. If
      <code>tomcatAuthorization</code> is set to <code>true</code> this
      attribute has no effect.</p>
    </td></tr><tr id="Attributes_Standard Implementations_tomcatAuthorization"><td><code class="attributeName">tomcatAuthorization</code></td><td>
      <p>If set to <code>true</code>, the authenticated principal will be
      propagated from the native webserver and considered already authenticated
      in Tomcat. If the web application has one or more security constraints,
      authorization will then be performed by Tomcat and roles assigned to the
      authenticated principal. If the appropriate Tomcat Realm for the request
      does not recognise the provided user name, a Principal will be still be
      created but it will have no roles. The default value is
      <code>false</code>.</p>
    </td></tr><tr id="Attributes_Standard Implementations_useVirtualThreads"><td><code class="attributeName">useVirtualThreads</code></td><td>
      <p>(bool) Use this attribute to enable or disable usage of virtual threads
      with the internal executor. If an executor is associated with this
      connector, this attribute is ignored. The default value is
      <code>false</code>.</p>
    </td></tr></table>

  </div></div>

  <div class="subsection"><h4 id="Java_TCP_socket_attributes">Java TCP socket attributes</h4><div class="text">

    <p>The NIO and NIO2 implementation support the following Java TCP socket
    attributes in addition to the common Connector and HTTP attributes listed
    above.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Java TCP socket attributes_socket.rxBufSize"><td><code class="attributeName">socket.rxBufSize</code></td><td>
        <p>(int)The socket receive buffer (SO_RCVBUF) size in bytes. JVM default
        used if not set.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.txBufSize"><td><code class="attributeName">socket.txBufSize</code></td><td>
        <p>(int)The socket send buffer (SO_SNDBUF) size in bytes. JVM default
        used if not set. Care should be taken if explicitly setting this value.
        Very poor performance has been observed on some JVMs with values less
        than ~8k.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.tcpNoDelay"><td><code class="attributeName">socket.tcpNoDelay</code></td><td>
        <p>(bool)This is equivalent to standard attribute
        <strong>tcpNoDelay</strong>.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.soKeepAlive"><td><code class="attributeName">socket.soKeepAlive</code></td><td>
        <p>(bool)Boolean value for the socket's keep alive setting
        (SO_KEEPALIVE). JVM default used if not set.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.ooBInline"><td><code class="attributeName">socket.ooBInline</code></td><td>
        <p>(bool)Boolean value for the socket OOBINLINE setting. JVM default
        used if not set.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.soReuseAddress"><td><code class="attributeName">socket.soReuseAddress</code></td><td>
        <p>(bool)Boolean value for the sockets reuse address option
        (SO_REUSEADDR). JVM default used if not set.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.soLingerOn"><td><code class="attributeName">socket.soLingerOn</code></td><td>
        <p>(bool)Boolean value for the sockets so linger option (SO_LINGER).
        A value for the standard attribute <strong>connectionLinger</strong>
        that is &gt;=0 is equivalent to setting this to <code>true</code>.
        A value for the standard attribute <strong>connectionLinger</strong>
        that is &lt;0 is equivalent to setting this to <code>false</code>.
        Both this attribute and <code>soLingerTime</code> must be set else the
        JVM defaults will be used for both.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.soLingerTime"><td><code class="attributeName">socket.soLingerTime</code></td><td>
        <p>(int)Value in seconds for the sockets so linger option (SO_LINGER).
        This is equivalent to standard attribute
        <strong>connectionLinger</strong>.
        Both this attribute and <code>soLingerOn</code> must be set else the
        JVM defaults will be used for both.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.soTimeout"><td><code class="attributeName">socket.soTimeout</code></td><td>
        <p>This is equivalent to standard attribute
        <strong>connectionTimeout</strong>.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.performanceConnectionTime"><td><code class="attributeName">socket.performanceConnectionTime</code></td><td>
        <p>(int)The first value for the performance settings. See
        <a href="https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html#setPerformancePreferences(int,%20int,%20int)">Socket Performance Options</a>
        All three performance attributes must be set else the JVM defaults will
        be used for all three.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.performanceLatency"><td><code class="attributeName">socket.performanceLatency</code></td><td>
        <p>(int)The second value for the performance settings. See
        <a href="https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html#setPerformancePreferences(int,%20int,%20int)">Socket Performance Options</a>
        All three performance attributes must be set else the JVM defaults will
        be used for all three.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.performanceBandwidth"><td><code class="attributeName">socket.performanceBandwidth</code></td><td>
        <p>(int)The third value for the performance settings. See
        <a href="https://docs.oracle.com/javase/8/docs/api/java/net/Socket.html#setPerformancePreferences(int,%20int,%20int)">Socket Performance Options</a>
        All three performance attributes must be set else the JVM defaults will
        be used for all three.</p>
      </td></tr><tr id="Attributes_Java TCP socket attributes_socket.unlockTimeout"><td><code class="attributeName">socket.unlockTimeout</code></td><td>
        <p>(int) The timeout for a socket unlock. When a connector is stopped, it will try to release the acceptor thread by opening a connector to itself.
           The default value is <code>250</code> and the value is in milliseconds</p>
      </td></tr></table>
  </div></div>

  <div class="subsection"><h4 id="NIO_specific_configuration">NIO specific configuration</h4><div class="text">

    <p>The following attributes are specific to the NIO connector.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_NIO specific configuration_socket.directBuffer"><td><code class="attributeName">socket.directBuffer</code></td><td>
        <p>(bool)Boolean value, whether to use direct ByteBuffers or java mapped
        ByteBuffers. Default is <code>false</code>.<br>
        When you are using direct buffers, make sure you allocate the
        appropriate amount of memory for the direct memory space. On Sun's JDK
        that would be something like <code>-XX:MaxDirectMemorySize=256m</code>.
        </p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.appReadBufSize"><td><code class="attributeName">socket.appReadBufSize</code></td><td>
        <p>(int)Each connection that is opened up in Tomcat get associated with
        a read ByteBuffer. This attribute controls the size of this buffer. By
        default this read buffer is sized at <code>8192</code> bytes. For lower
        concurrency, you can increase this to buffer more data. For an extreme
        amount of keep alive connections, decrease this number or increase your
        heap size.</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.appWriteBufSize"><td><code class="attributeName">socket.appWriteBufSize</code></td><td>
        <p>(int)Each connection that is opened up in Tomcat get associated with
        a write ByteBuffer. This attribute controls the size of this buffer. By
        default this write buffer is sized at <code>8192</code> bytes. For low
        concurrency you can increase this to buffer more response data. For an
        extreme amount of keep alive connections, decrease this number or
        increase your heap size.<br>
        The default value here is pretty low, you should up it if you are not
        dealing with tens of thousands concurrent connections.</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.bufferPool"><td><code class="attributeName">socket.bufferPool</code></td><td>
        <p>(int)The NIO connector uses a class called NioChannel that holds
        elements linked to a socket. To reduce garbage collection, the NIO
        connector caches these channel objects. This value specifies the size of
        this cache. The default value is <code>500</code>, and represents that
        the cache will hold 500 NioChannel objects. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.bufferPoolSize"><td><code class="attributeName">socket.bufferPoolSize</code></td><td>
        <p>(int)The NioChannel pool can also be size based, not used object
        based. The size is calculated as follows:<br>
        NioChannel
        <code>buffer size = read buffer size + write buffer size</code><br>
        SecureNioChannel <code>buffer size = application read buffer size +
        application write buffer size + network read buffer size +
        network write buffer size</code><br>
        The value is in bytes, the default value is <code>1024*1024*100</code>
        (100 MiB).</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.processorCache"><td><code class="attributeName">socket.processorCache</code></td><td>
        <p>(int)Tomcat will cache SocketProcessor objects to reduce garbage
        collection. The integer value specifies how many objects to keep in the
        cache at most. The default is <code>500</code>. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.keyCache"><td><code class="attributeName">socket.keyCache</code></td><td>
        <p>(int)Tomcat will cache KeyAttachment objects to reduce garbage
        collection. The integer value specifies how many objects to keep in the
        cache at most. The default is <code>500</code>. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr><tr id="Attributes_NIO specific configuration_socket.eventCache"><td><code class="attributeName">socket.eventCache</code></td><td>
        <p>(int)Tomcat will cache PollerEvent objects to reduce garbage
        collection. The integer value specifies how many objects to keep in the
        cache at most. The default is <code>500</code>. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr></table>
  </div></div>

  <div class="subsection"><h4 id="NIO2_specific_configuration">NIO2 specific configuration</h4><div class="text">

    <p>The following attributes are specific to the NIO2 connector.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_NIO2 specific configuration_useCaches"><td><code class="attributeName">useCaches</code></td><td>
        <p>(bool)Use this attribute to enable or disable object caching to
        reduce the amount of GC objects produced.
        The default value is <code>false</code>.</p>
      </td></tr><tr id="Attributes_NIO2 specific configuration_socket.directBuffer"><td><code class="attributeName">socket.directBuffer</code></td><td>
        <p>(bool)Boolean value, whether to use direct ByteBuffers or java mapped
        ByteBuffers. Default is <code>false</code>.<br>
        When you are using direct buffers, make sure you allocate the
        appropriate amount of memory for the direct memory space. On Sun's JDK
        that would be something like <code>-XX:MaxDirectMemorySize=256m</code>.
        </p>
      </td></tr><tr id="Attributes_NIO2 specific configuration_socket.appReadBufSize"><td><code class="attributeName">socket.appReadBufSize</code></td><td>
        <p>(int)Each connection that is opened up in Tomcat get associated with
        a read ByteBuffer. This attribute controls the size of this buffer. By
        default this read buffer is sized at <code>8192</code> bytes. For lower
        concurrency, you can increase this to buffer more data. For an extreme
        amount of keep alive connections, decrease this number or increase your
        heap size.</p>
      </td></tr><tr id="Attributes_NIO2 specific configuration_socket.appWriteBufSize"><td><code class="attributeName">socket.appWriteBufSize</code></td><td>
        <p>(int)Each connection that is opened up in Tomcat get associated with
        a write ByteBuffer. This attribute controls the size of this buffer. By
        default this write buffer is sized at <code>8192</code> bytes. For low
        concurrency you can increase this to buffer more response data. For an
        extreme amount of keep alive connections, decrease this number or
        increase your heap size.<br>
        The default value here is pretty low, you should up it if you are not
        dealing with tens of thousands concurrent connections.</p>
      </td></tr><tr id="Attributes_NIO2 specific configuration_socket.bufferPoolSize"><td><code class="attributeName">socket.bufferPoolSize</code></td><td>
        <p>(int)The NIO2 connector uses a class called Nio2Channel that holds
        elements linked to a socket. To reduce garbage collection, the NIO
        connector caches these channel objects. This value specifies the size of
        this cache. The default value is <code>500</code>, and represents that
        the cache will hold 500 Nio2Channel objects. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr><tr id="Attributes_NIO2 specific configuration_socket.processorCache"><td><code class="attributeName">socket.processorCache</code></td><td>
        <p>(int)Tomcat will cache SocketProcessor objects to reduce garbage
        collection. The integer value specifies how many objects to keep in the
        cache at most. The default is <code>500</code>. Other values are
        <code>-1</code> for unlimited cache and <code>0</code> for no cache.</p>
      </td></tr></table>
  </div></div>

  <div class="subsection"><h4 id="APR/native_specific_configuration">APR/native specific configuration</h4><div class="text">

    <p>The APR/native implementation supports the following attributes in
    addition to the common Connector and AJP attributes listed above.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_APR/native specific configuration_ipv6v6only"><td><code class="attributeName">ipv6v6only</code></td><td>
        <p>If listening on an IPv6 address on a dual stack system, should the
        connector only listen on the IPv6 address? If not specified the default
        is <code>false</code> and the connector will listen on the IPv6 address
        and the equivalent IPv4 address if present.</p>
      </td></tr><tr id="Attributes_APR/native specific configuration_pollTime"><td><code class="attributeName">pollTime</code></td><td>
        <p>Duration of a poll call in microseconds. Lowering this value will
        slightly decrease latency of connections being kept alive in some cases
        , but will use more CPU as more poll calls are being made. The default
        value is 2000 (2ms).
        </p>
    </td></tr></table>

  </div></div>

</div><h3 id="Nested_Components">Nested Components</h3><div class="text">

  <p>None at this time.</p>

</div><h3 id="Special_Features">Special Features</h3><div class="text">

  <div class="subsection"><h4 id="Proxy_Support">Proxy Support</h4><div class="text">

  <p>The <code>proxyName</code> and <code>proxyPort</code> attributes can
  be used when Tomcat is run behind a proxy server.  These attributes
  modify the values returned to web applications that call the
  <code>request.getServerName()</code> and <code>request.getServerPort()</code>
  methods, which are often used to construct absolute URLs for redirects.
  Without configuring these attributes, the values returned would reflect
  the server name and port on which the connection from the proxy server
  was received, rather than the server name and port to whom the client
  directed the original request.</p>

  <p>For more information, see the
  <a href="../proxy-howto.html">Proxy Support How-To</a>.</p>

  </div></div>

  <div class="subsection"><h4 id="Connector_Comparison">Connector Comparison</h4><div class="text">

    <p>Below is a small chart that shows how the connectors differ.</p>

    <table class="defaultTable" style="text-align: center;">
      <tr>
        <th></th>
        <th style="text-align: center;">Java Nio Connector<br>NIO</th>
        <th style="text-align: center;">Java Nio2 Connector<br>NIO2</th>
        <th style="text-align: center;">APR/native Connector<br>APR</th>
      </tr>
      <tr>
        <th>Classname</th>
        <td><code class="noHighlight">AjpNioProtocol</code></td>
        <td><code class="noHighlight">AjpNio2Protocol</code></td>
        <td><code class="noHighlight">AjpAprProtocol</code></td>
      </tr>
      <tr>
        <th>Tomcat Version</th>
        <td>7.x onwards</td>
        <td>8.x onwards</td>
        <td>5.5.x onwards</td>
      </tr>
      <tr>
        <th>Support Polling</th>
        <td>YES</td>
        <td>YES</td>
        <td>YES</td>
      </tr>
      <tr>
        <th>Polling Size</th>
        <td><code class="noHighlight">maxConnections</code></td>
        <td><code class="noHighlight">maxConnections</code></td>
        <td><code class="noHighlight">maxConnections</code></td>
      </tr>
      <tr>
        <th>Read Request Headers</th>
        <td>Blocking</td>
        <td>Blocking</td>
        <td>Blocking</td>
      </tr>
      <tr>
        <th>Read Request Body</th>
        <td>Blocking</td>
        <td>Blocking</td>
        <td>Blocking</td>
      </tr>
      <tr>
        <th>Write Response Headers and Body</th>
        <td>Blocking</td>
        <td>Blocking</td>
        <td>Blocking</td>
      </tr>
      <tr>
        <th>Wait for next Request</th>
        <td>Non Blocking</td>
        <td>Non Blocking</td>
        <td>Non Blocking</td>
      </tr>
      <tr>
        <th>Max Connections</th>
        <td><code class="noHighlight">maxConnections</code></td>
        <td><code class="noHighlight">maxConnections</code></td>
        <td><code class="noHighlight">maxConnections</code></td>
      </tr>
    </table>

  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>