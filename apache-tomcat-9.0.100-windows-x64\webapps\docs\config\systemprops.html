<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.100) - System Properties</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>System Properties</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Property_replacements">Property replacements</a></li><li><a href="#Clustering">Clustering</a></li><li><a href="#Expression_Language">Expression Language</a></li><li><a href="#Jasper">Jasper</a></li><li><a href="#Security">Security</a></li><li><a href="#Specifications">Specifications</a></li><li><a href="#Sessions">Sessions</a></li><li><a href="#Logging">Logging</a></li><li><a href="#JAR_Scanning">JAR Scanning</a></li><li><a href="#Websockets">Websockets</a></li><li><a href="#Other">Other</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>The following sections list the system properties that may be set to modify
  the default Tomcat behaviour.</p>
</div><h3 id="Property_replacements">Property replacements</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.tomcat.util.digester. PROPERTY_SOURCE</code></td><td>
      <p>Set this to a comma separated list of fully qualified name of classes
         that implement
         <code>org.apache.tomcat.util.IntrospectionUtils.PropertySource</code>.
         Required to have a public constructor with no arguments.</p>
      <p>Use this to add a property source, that will be invoked when
         <code>${parameter:-default-value}</code> denoted parameters (with
         optional default values) are found in the XML files that Tomcat
         parses.</p>
      <p>Property replacement from the specified property source on the JVM
         system properties can also be done using the
         <code>REPLACE_SYSTEM_PROPERTIES</code> system property.</p>
      <p><code>org.apache.tomcat.util.digester.ServiceBindingPropertySource</code>
        can be used to replace parameters from any Kubernetes service bindings
        that follows the <a href="https://servicebinding.io/">servicebinding.io</a> spec</p>
      <p><code>org.apache.tomcat.util.digester.EnvironmentPropertySource</code>
         can be used to replace parameters from the process' environment
         variables, e.g. injected ConfigMaps or Secret objects in container
         based systems like OpenShift or Kubernetes.</p>
      <p><code>org.apache.tomcat.util.digester.SystemPropertySource</code>
         does replacement with system properties. It is always enabled,
         but can also be specified as part of the property value.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.digester. REPLACE_SYSTEM_PROPERTIES</code></td><td>
      <p>Set this boolean system property to <code>true</code> to cause
         property replacement from the digester property source on the JVM
         system properties.</p>
    </td></tr></table>

</div><h3 id="Clustering">Clustering</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.catalina. tribes.dns_lookups</code></td><td>
      <p>This system property is deprecated and will be removed without
      replacement in Apache Tomcat 10 onwards.</p>
      <p>If <code>true</code>, the clustering module will attempt to use DNS to
      resolve any host names provided in the cluster configuration.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr></table>

</div><h3 id="Expression_Language">Expression Language</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.el. GET_CLASSLOADER_USE_PRIVILEGED</code></td><td>
      <p>Controls whether the EL API classes make use of a privileged block to
      obtain the thread context class loader. When using the EL API within
      Apache Tomcat this does not need to be set as all calls are already
      wrapped in a privileged block further up the stack. It may be required if
      using the EL API under a SecurityManager outside of Apache Tomcat.</p>
      <p>If not specified, the default of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.BeanELResolver. CACHE_SIZE</code></td><td>
      <p>The number of javax.el.BeanELResolver.BeanProperties objects that will
      be cached by the EL Parser.</p>
      <p>If not specified, the default of <code>1000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.ExpressionBuilder. CACHE_SIZE</code></td><td>
      <p>The number of parsed EL expressions that will be cached by the EL
      Parser.</p>
      <p>If not specified, the default of <code>5000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.parser. COERCE_TO_ZERO</code></td><td>
      <p>If <code>true</code>, when coercing <code>null</code>s to objects of
      type Number, Character or Boolean the result will be <code>0</code> for
      Number and Character types and <code>false</code> for Boolean as required
      by the EL 2.2 and earlier specifications. If this property is
      <code>false</code> the result of the coercion will be <code>null</code> as
      required by the EL 3.0 specification.</p>
      <p>If not specified, the default value of <code>false</code> will be
      used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.parser. SKIP_IDENTIFIER_CHECK</code></td><td>
      <p>If <code>true</code>, when parsing expressions, identifiers will not be
      checked to ensure that they conform to the Java Language Specification for
      Java identifiers.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr></table>
</div><h3 id="Jasper">Jasper</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.jasper.compiler. Generator.POOL_TAGS_WITH_EXTENDS</code></td><td>
      <p>By default, JSPs that use their own base class via the extends
      attribute of the page directive, will have Tag pooling disabled since
      Jasper cannot guarantee that the necessary initialisation will have taken
      place. This can have a negative impact on performance. Providing the
      alternative base class calls _jspInit() from Servlet.init(), setting  this
      property to <code>true</code> will enable pooling with an alternative base
      class. If the alternative base class does not call _jspInit() and this
      property is <code>true</code>, NPEs will occur when attempting to use
      tags.</p>
      <p>If not specified, the default value of <code>false</code> will be used.
      </p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.compiler. Generator.STRICT_GET_PROPERTY</code></td><td>
      <p>If <code>true</code>, the requirement to have the object referenced in
      <code>jsp:getProperty</code> action to be previously "introduced"
      to the JSP processor, as specified in the chapter JSP.5.3 of JSP 2.0 and
      later specifications, is enforced.</p>
      <p>If not specified, the specification compliant default of
      <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.compiler. Generator.VAR_EXPRESSIONFACTORY</code></td><td>
      <p>The name of the variable to use for the expression language expression
      factory.</p>
      <p>If not specified, the default value of <code>_el_expressionfactory</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.compiler. Generator.VAR_INSTANCEMANAGER</code></td><td>
      <p>The name of the variable to use for the instance manager factory.</p>
      <p>If not specified, the default value of <code>_jsp_instancemanager</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.compiler. Parser.STRICT_WHITESPACE</code></td><td>
      <p>If <code>false</code> the requirements for whitespace before an
      attribute name will be relaxed so that the lack of whitespace will not
      cause an error.</p>
      <p>If not specified, the specification compliant default of
      <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.runtime. BodyContentImpl.BUFFER_SIZE</code></td><td>
      <p>The size (in characters) to use when creating a tag buffer.</p>
      <p>If not specified, the default value of
      <code>org.apache.jasper.Constants.DEFAULT_TAG_BUFFER_SIZE</code> (512)
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.runtime. BodyContentImpl.LIMIT_BUFFER</code></td><td>
      <p>If <code>true</code>, any tag buffer that expands beyond
      <code>org.apache.jasper.runtime.BodyContentImpl.BUFFER_SIZE</code> will be
      destroyed and a new buffer created.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.runtime. JspFactoryImpl.USE_POOL</code></td><td>
      <p>If <code>true</code>, a ThreadLocal <code>PageContext</code> pool will
      be used.</p>
      <p>If not specified, the default value of <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.runtime. JspFactoryImpl.POOL_SIZE</code></td><td>
      <p>The size of the ThreadLocal <code>PageContext</code>.</p>
      <p>If not specified, the default value of <code>8</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. JSP_SERVLET_BASE</code></td><td>
      <p>The base class of the Servlets generated from the JSPs.</p>
      <p>If not specified, the default value of
      <code>org.apache.jasper.runtime.HttpJspBase</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. SERVICE_METHOD_NAME</code></td><td>
      <p>The name of the service method called by the base class.</p>
      <p>If not specified, the default value of <code>_jspService</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. SERVLET_CLASSPATH</code></td><td>
      <p>The name of the ServletContext attribute that provides the classpath
      for the JSP.</p>
      <p>If not specified, the default value of
      <code>org.apache.catalina.jsp_classpath</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. JSP_FILE</code></td><td>
      <p>The name of the request attribute for <code>&lt;jsp-file&gt;</code>
      element of a servlet definition. If present on a request, this overrides
      the value returned by <code>request.getServletPath()</code> to select the
      JSP page to be executed.</p>
      <p>If not specified, the default value of
      <code>org.apache.catalina.jsp_file</code> will be used.</p>
      <p><strong>Deprecated:</strong> This will be removed in Tomcat 9.0.x
      onwards. It is replaced by the use of the jspFile servlet initialisation
      parameter</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. PRECOMPILE</code></td><td>
      <p>The name of the query parameter that causes the JSP engine to just
      pregenerate the servlet but not invoke it.</p>
      <p>If not specified, the default value of <code>jsp_precompile</code>
      will be used, as defined by JSP specification (JSP.11.4.2).</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. JSP_PACKAGE_NAME</code></td><td>
      <p>The default package name for compiled JSPs.</p>
      <p>If not specified, the default value of <code>org.apache.jsp</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. TAG_FILE_PACKAGE_NAME</code></td><td>
      <p>The default package name for tag handlers generated from tag files.</p>
      <p>If not specified, the default value of <code>org.apache.jsp.tag</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. ALT_DD_ATTR</code></td><td>
      <p>The servlet context attribute under which the alternate deployment
      descriptor for this web application is stored.</p>
      <p>If not specified, the default value of
      <code>org.apache.catalina.deploy.alt_dd</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. TEMP_VARIABLE_NAME_PREFIX</code></td><td>
      <p>Prefix to use for generated temporary variable names.</p>
      <p>If not specified, the default value of <code>_jspx_temp</code>
      will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.jasper.Constants. USE_INSTANCE_MANAGER_FOR_TAGS</code></td><td>
      <p>If <code>true</code>, the instance manager is used to obtain tag
      handler instances.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr></table>

</div><h3 id="Security">Security</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.catalina.connector. RECYCLE_FACADES</code></td><td>
      <p>If this is <code>true</code> or if a security manager is in use a new
      facade object will be created for each request.</p>
      <p>If not specified, the default value of <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.connector. CoyoteAdapter.ALLOW_BACKSLASH</code></td><td>
      <p>If this is <code>true</code> the '\' character will be permitted as a
      path delimiter.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.buf. UDecoder.ALLOW_ENCODED_SLASH</code></td><td>
      <p>Use of this system property is deprecated. It will be removed from
      Tomcat 10 onwards.</p>
      <p>If this system property is set to <code>true</code>, the default for
      the <code>encodedSolidusHandling</code> attribute of all Connectors will
      be changed from <code>reject</code> to <code>decode</code>. If decoded, it
      will be treated a path delimiter.</p>
    </td></tr></table>

</div><h3 id="Specifications">Specifications</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.catalina. STRICT_SERVLET_COMPLIANCE</code></td><td>
      <p>The default value of this system property is <code>false</code>.</p>
      <p>If this is <code>true</code> the default values will be changed for:</p>
      <ul>
      <li><code>org.apache.catalina.core.<br>ApplicationContext.GET_RESOURCE_REQUIRE_SLASH</code></li>
      <li><code>org.apache.catalina.core.<br>ApplicationDispatcher.WRAP_SAME_OBJECT</code></li>
      <li><code>org.apache.catalina.core.<br>StandardHostValve.ACCESS_SESSION</code></li>
      <li><code>org.apache.catalina.session.<br>StandardSession.ACTIVITY_CHECK</code></li>
      <li><code>org.apache.catalina.session.<br>StandardSession.LAST_ACCESS_AT_START</code></li>
      <li><code>org.apache.tomcat.util.http.<br>ServerCookie.STRICT_NAMING</code></li>
      <li>The <code>resourceOnlyServlets</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>tldValidation</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>useRelativeRedirects</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>xmlNamespaceAware</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>xmlValidation</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      </ul>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.connector. Response.ENFORCE_ENCODING_IN_GET_WRITER</code></td><td>
      <p>If this is <code>true</code> then
      a call to <code>Response.getWriter()</code> if no character encoding
      has been specified will result in subsequent calls to
      <code>Response.getCharacterEncoding()</code> returning
      <code>ISO-8859-1</code> and the <code>Content-Type</code> response header
      will include a <code>charset=ISO-8859-1</code> component. (SRV.15.2.22.1)</p>
      <p>If not specified, the default specification compliant value of
      <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.core.ApplicationContext .GET_RESOURCE_REQUIRE_SLASH</code></td><td>
      <p>If this is <code>true</code> then the path passed to
      <code>ServletContext.getResource()</code> or
      <code>ServletContext.getResourceAsStream()</code> must start with
      "/". If <code>false</code>, code like
      <code>getResource("myfolder/myresource.txt")</code> will work as Tomcat
      will prepend "/" to the provided path.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.core. ApplicationDispatcher.WRAP_SAME_OBJECT</code></td><td>
      <p>If this is <code>true</code> then any wrapped request or response
      object passed to an application dispatcher will be checked to ensure that
      it has wrapped the original request or response.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.websocket. STRICT_SPEC_COMPLIANCE</code></td><td>
      <p>The default value of this system property is <code>false</code>.</p>
      <p>If this is <code>true</code> the default values will be changed for:</p>
      <ul>
        <li><code>org.apache.tomcat.websocket.server#isEnforceNoAddAfterHandshake</code>
        (default changes from <code>false</code> to <code>true</code>)</li>
      </ul>
      <p>This system property is deprecated and will be removed in Tomcat
      10.1.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.http. ServerCookie.STRICT_NAMING</code></td><td>
      <p> If this is <code>true</code> then the requirements of the Servlet specification
      that Cookie names must adhere to RFC2109 will be enforced. If this is
      <code>false</code> the naming rules specified in RFC6265 (allow the leading "$")
      will be used.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr></table>

</div><h3 id="Sessions">Sessions</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.catalina.authenticator.                     Constants.SSO_SESSION_COOKIE_NAME</code></td><td>
      <p>An alternative name for the single sign on session cookie. Defaults to
      <code>JSESSIONIDSSO</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.core. StandardHostValve.ACCESS_SESSION</code></td><td>
      <p>If this is <code>true</code>, every request that is associated with a
      session will cause the session's last accessed time to be updated
      regardless of whether or not the request explicitly accesses the session.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.session. StandardSession.ACTIVITY_CHECK</code></td><td>
      <p>If this is <code>true</code>, Tomcat will track the number of active
      requests for each session. When determining if a session is valid, any
      session with at least one active request will always be considered valid.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.session. StandardSession.LAST_ACCESS_AT_START</code></td><td>
      <p>If this is <code>true</code>, the last accessed time for sessions will
      be calculated from the beginning of the previous request. If
      <code>false</code>, the last accessed time for sessions will be calculated
      from the end of the previous request. This also affects how the idle time
      is calculated.</p>
      <p>If <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code> is set to
      <code>true</code>, the default of this setting will be <code>true</code>,
      else the default value will be <code>false</code>.</p>
    </td></tr></table>

</div><h3 id="Logging">Logging</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.juli.formatter</code></td><td>
      <p>If no logging configuration file is specified and no logging configuration class is specified
         using the <code>java.util.logging.config.class</code> and <code>java.util.logging.config.file</code>
         properties the default logging framework <code>org.apache.juli</code> will use the default
         <code>java.util.logging.SimpleFormatter</code> for all console output.
         To simply override the console output formatter, one can use the described property. Example:
         <code>-Dorg.apache.juli.formatter=org.apache.juli.OneLineFormatter</code></p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli. AsyncMaxRecordCount</code></td><td>
      <p>The maximum number of log records that the JULI AsyncFileHandler will queue in memory.
         New records are added to the queue and get asynchronously removed from the queue
         and written to the files by a single writer thread.
         When the queue is full and a new record is being logged
         the log record will be handled based on the <code>org.apache.juli.AsyncOverflowDropType</code> setting.</p>
      <p>The default value is <code>10000</code> records.
         This number represents the global number of records, not on a per handler basis.
      </p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli. AsyncOverflowDropType</code></td><td>
      <p>When the queue of log records of the JULI AsyncFileHandler is full,
         new log records are handled according to the following setting:
      </p>
         <ul>
           <li><code>1</code> - the newest record in the queue will be dropped and not logged</li>
           <li><code>2</code> - the oldest record in the queue will be dropped and not logged</li>
           <li><code>3</code> - suspend the logging thread until older records got written to the log file and the queue is no longer full.
                                This is the only setting that ensures that no messages get lost.</li>
           <li><code>4</code> - drop the current log record</li>
         </ul>
      <p>The default value is <code>1</code> (drop the newest record in the queue).</p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli.logging. UserDataHelper.CONFIG</code></td><td>
      <p>The type of logging to use for errors generated by invalid input data.
         The options are: <code>DEBUG_ALL</code>, <code>INFO_THEN_DEBUG</code>,
         <code>INFO_ALL</code> and <code>NONE</code>. When
         <code>INFO_THEN_DEBUG</code> is used, the period for which errors are
         logged at DEBUG rather than INFO is controlled by the system property
         <code>org.apache.juli.logging.UserDataHelper.SUPPRESSION_TIME</code>.
         </p>
      <p>The default value is <code>INFO_THEN_DEBUG</code>.</p>
      <p>The errors currently logged using this system are:</p>
         <ul>
         <li>invalid cookies;</li>
         <li>invalid parameters;</li>
         <li>too many headers, too many parameters (hitting
           <code>maxHeaderCount</code> or <code>maxParameterCount</code> limits
           of a <a href="http.html">connector</a>).</li>
         <li>invalid host names</li>
         <li>HTTP/2 stream closures</li>
         </ul>
         <p>Other errors triggered by invalid input data may be added to this
         system in later versions.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli.logging. UserDataHelper.SUPPRESSION_TIME</code></td><td>
      <p>When using <code>INFO_THEN_DEBUG</code> for
         <code>org.apache.juli.logging.UserDataHelper.CONFIG</code> this system
         property controls how long messages are logged at DEBUG after a message
         has been logged at INFO. Once this period has elapsed, the next message
         will be logged at INFO followed by a new suppression period where
         messages are logged at DEBUG and so on. The value is measured
         in seconds.</p>
      <p>A value of <code>0</code> is equivalent to using <code>INFO_ALL</code>
         for <code>org.apache.juli.logging.UserDataHelper.CONFIG</code>.</p>
      <p>A negative value means an infinite suppression period.</p>
      <p>The default value is <code>86400</code> (24 hours).</p>
    </td></tr></table>

</div><h3 id="JAR_Scanning">JAR Scanning</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">tomcat.util.scan. StandardJarScanFilter.jarsToSkip</code></td><td>
      <p>A list of comma-separated file name patterns that is used as the default
         value for <code>pluggabilitySkip</code> and <code>tldSkip</code>
         attributes of the standard
         <a href="jar-scan-filter.html">JarScanFilter</a> implementation.</p>
      <p>The coded default is empty, however the system property is set in
         a default Tomcat installation via the
         <code>$CATALINA_BASE/conf/catalina.properties</code> file.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.scan. StandardJarScanFilter.jarsToScan</code></td><td>
      <p>A list of comma-separated file name patterns that is used as the default
         value for <code>pluggabilityScan</code> and <code>tldScan</code>
         attributes of the standard
         <a href="jar-scan-filter.html">JarScanFilter</a> implementation.</p>
      <p>The coded default is empty, however the system property is set in
         a default Tomcat installation via the
         <code>$CATALINA_BASE/conf/catalina.properties</code> file.</p>
    </td></tr></table>

</div><h3 id="Websockets">Websockets</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.ALLOW_UNSUPPORTED_EXTENSIONS</code></td><td>
      <p>If <code>true</code>, allow unknown extensions to be declared by
      the user.</p>
      <p>The default value is <code>false</code>.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_BUFFER_SIZE</code></td><td>
      <p>The default size for buffers used in the Websockets container.</p>
      <p>The default value is <code>8192</code> which corresponds to 8 KiB.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_ORIGIN_HEADER_VALUE</code></td><td>
      <p>Default value of the origin header that will be sent by the client
         during the upgrade handshake.</p>
      <p>The default is null so that no origin header is sent.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_PROCESS_PERIOD</code></td><td>
      <p>The number of periodic ticks between periodic processing which
         involves in particular session expiration checks.</p>
      <p>The default value is <code>10</code> which corresponds to 10
         seconds.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DISABLE_BUILTIN_EXTENSIONS</code></td><td>
      <p>If <code>true</code>, disable all built-in extensions provided by the
         server, such as message compression.</p>
      <p>The default value is <code>false</code>.</p>
    </td></tr></table>

</div><h3 id="Other">Other</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">catalina.useNaming</code></td><td>
      <p>If this is <code>false</code> it will override the
      <code>useNaming</code> attribute for all <a href="context.html">
      Context</a> elements.</p>
    </td></tr><tr><td><code class="propertyName">javax.sql.DataSource.Factory</code></td><td>
      <p>The class name of the factory to use to create resources of type
      <code>javax.sql.DataSource</code>. If not specified the default of
      <code>org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory</code> is used
      which is a package renamed (to avoid conflicts) copy of
      <a href="https://commons.apache.org/dbcp">Apache Commons DBCP 2</a>.</p>
    </td></tr><tr><td><code class="propertyName">javax.mail.Session.Factory</code></td><td>
      <p>The class name of the factory to use to create resources of type
      <code>javax.mail.Session</code>. If not specified the default of
      <code>org.apache.naming.factory.MailSessionFactory</code> is used.</p>
    </td></tr><tr><td><code class="propertyName">jvmRoute</code></td><td>
      <p>Deprecated. Use the <code>jvmRoute</code> attribute of the
      <a href="engine.html">Engine</a> element. This will be removed in Tomcat
      10.1.</p>
      <p>Provides a default value for the <code>jvmRoute</code> attribute of the
      <a href="engine.html">Engine</a> element. It does not override the value
      configured on the <a href="engine.html">Engine</a> element.</p>
    </td></tr><tr><td><code class="propertyName">catalina.config</code></td><td>
      <p>The location from which to load the catalina.properties configuration
      file. This may be an absolute URL, a relative (to the current working
      directory) URL or an alternative file name in which case Tomcat will
      attempt to load the file from the default location of
      <code>$CATALINA_BASE/conf/</code>.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.byte.enabled</code></td><td>
      <p>If <code>true</code>, the String cache is enabled for
      <code>ByteChunk</code>.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.char.enabled</code></td><td>
      <p>If <code>true</code>, the String cache is enabled for
      <code>CharChunk</code>.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.trainThreshold</code></td><td>
      <p>The number of times <code>toString()</code> must be called before the
      cache is activated.</p>
      <p>If not specified, the default value of <code>20000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.cacheSize</code></td><td>
      <p>The size of the String cache.</p>
      <p>If not specified, the default value of <code>200</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.buf.UriUtil. WAR_SEPARATOR</code></td><td>
      <p>The character to use to separate the WAR file and WAR content parts of
      a WAR URL using the custom WAR scheme provided by Tomcat. This is
      equivalent to how <code>!</code> is used in JAR URLs.</p>
      <p>If not specified, the default value of <code>*</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.maxStringSize</code></td><td>
      <p>The maximum length of String that will be cached.</p>
      <p>If not specified, the default value of <code>128</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util. http.FastHttpDateFormat.CACHE_SIZE</code></td><td>
      <p>The size of the cache to use parsed and formatted date value.</p>
      <p>If not specified, the default value of <code>1000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util. net.NioSelectorShared</code></td><td>
      <p>If <code>true</code>, use a shared selector for servlet write/read.</p>
      <p>If not specified, the default value of <code>true</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. EXIT_ON_INIT_FAILURE</code></td><td>
      <p>If <code>true</code>, the server will exit if an exception happens
      during the server initialization phase. To support this feature, this
      system property is used as the default for the
      <strong>throwOnFailure</strong> attribute of a Connector.</p>
      <p>If not specified, the default value of <code>false</code> will be
      used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. RealmRuleSet.MAX_NESTED_REALM_LEVELS</code></td><td>
      <p>The CombinedRealm allows nested Realms. This property controls the
      maximum permitted number of levels of nesting.</p>
      <p>If not specified, the default value of <code>3</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. CredentialHandlerRuleSet.MAX_NESTED_LEVELS</code></td><td>
      <p>The NestedCredentialHandler allows nested CredentialHandlers. This
      property controls the maximum permitted number of levels of nesting.</p>
      <p>If not specified, the default value of <code>3</code> will be used.</p>
    </td></tr></table>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>