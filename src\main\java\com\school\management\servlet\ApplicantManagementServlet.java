package com.school.management.servlet;

import com.school.management.dto.ActivistReviewDTO;
import com.school.management.entity.PartyApplicant;
import com.school.management.entity.PartyActivist;
import com.school.management.service.PartyApplicantService;
import com.school.management.service.ActivistReviewService;
import com.school.management.service.impl.PartyApplicantServiceImpl;
import com.school.management.service.impl.ActivistReviewServiceImpl;
import com.school.management.util.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 入党申请人管理Servlet
 */
@WebServlet("/api/applicant/*")
public class ApplicantManagementServlet extends HttpServlet {
    
    private PartyApplicantService applicantService;
    private ActivistReviewService reviewService;
    private SimpleDateFormat dateFormat;
    
    @Override
    public void init() throws ServletException {
        super.init();
        this.applicantService = new PartyApplicantServiceImpl();
        this.reviewService = new ActivistReviewServiceImpl();
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/list".equals(pathInfo)) {
                handleGetList(request, response);
            } else if (pathInfo != null && pathInfo.startsWith("/")) {
                String idStr = pathInfo.substring(1);
                try {
                    Integer id = Integer.parseInt(idStr);
                    handleGetById(request, response, id);
                } catch (NumberFormatException e) {
                    writeErrorResponse(response, "无效的ID格式");
                }
            } else {
                writeErrorResponse(response, "不支持的请求路径");
            }
        } catch (Exception e) {
            System.err.println("处理GET请求失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/add".equals(pathInfo)) {
                handleAdd(request, response);
            } else if ("/review".equals(pathInfo)) {
                handleActivistReview(request, response);
            } else {
                writeErrorResponse(response, "不支持的请求路径");
            }
        } catch (Exception e) {
            System.err.println("处理POST请求失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo != null && pathInfo.startsWith("/")) {
                String idStr = pathInfo.substring(1);
                try {
                    Integer id = Integer.parseInt(idStr);
                    handleUpdate(request, response, id);
                } catch (NumberFormatException e) {
                    writeErrorResponse(response, "无效的ID格式");
                }
            } else {
                writeErrorResponse(response, "不支持的请求路径");
            }
        } catch (Exception e) {
            System.err.println("处理PUT请求失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo != null && pathInfo.startsWith("/")) {
                String idStr = pathInfo.substring(1);
                try {
                    Integer id = Integer.parseInt(idStr);
                    handleDelete(request, response, id);
                } catch (NumberFormatException e) {
                    writeErrorResponse(response, "无效的ID格式");
                }
            } else {
                writeErrorResponse(response, "不支持的请求路径");
            }
        } catch (Exception e) {
            System.err.println("处理DELETE请求失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "服务器内部错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理获取申请人列表
     */
    private void handleGetList(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        try {
            // 获取分页参数
            int page = getIntParameter(request, "page", 1);
            int size = getIntParameter(request, "size", 10);
            
            // 获取搜索条件
            String name = request.getParameter("name");
            String idCard = request.getParameter("idCard");
            String grade = request.getParameter("grade");
            String status = request.getParameter("status");
            
            // 构建搜索条件
            Map<String, Object> conditions = new HashMap<>();
            if (name != null && !name.trim().isEmpty()) {
                conditions.put("name", name.trim());
            }
            if (idCard != null && !idCard.trim().isEmpty()) {
                conditions.put("idCard", idCard.trim());
            }
            if (grade != null && !grade.trim().isEmpty()) {
                conditions.put("grade", grade.trim());
            }
            if (status != null && !status.trim().isEmpty()) {
                conditions.put("status", status.trim());
            }
            
            // 查询数据
            List<PartyApplicant> applicants;
            int totalCount;
            
            if (conditions.isEmpty()) {
                // 无搜索条件，获取所有数据
                applicants = applicantService.getAll();
                totalCount = applicants.size();
            } else {
                // 有搜索条件，按条件查询
                applicants = applicantService.getByConditions(conditions);
                totalCount = applicants.size();
            }
            
            // 手动分页（实际项目中应该在数据库层面分页）
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, applicants.size());
            List<PartyApplicant> pageData = applicants.subList(startIndex, endIndex);
            
            // 构建分页信息
            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", page);
            pagination.put("pageSize", size);
            pagination.put("totalCount", totalCount);
            pagination.put("totalPages", (int) Math.ceil((double) totalCount / size));
            
            // 构建响应数据
            Map<String, Object> result = new HashMap<>();
            result.put("data", pageData);
            result.put("pagination", pagination);
            
            writeSuccessResponse(response, result, "查询成功");
            
        } catch (Exception e) {
            System.err.println("获取申请人列表失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "获取申请人列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理根据ID获取申请人
     */
    private void handleGetById(HttpServletRequest request, HttpServletResponse response, Integer id) 
            throws IOException {
        
        try {
            PartyApplicant applicant = applicantService.getById(id);
            if (applicant != null) {
                writeSuccessResponse(response, applicant, "查询成功");
            } else {
                writeErrorResponse(response, "未找到指定的申请人");
            }
        } catch (Exception e) {
            System.err.println("获取申请人详情失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "获取申请人详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理新增申请人
     */
    private void handleAdd(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        try {
            // 获取表单参数
            String name = request.getParameter("name");
            String idCard = request.getParameter("idCard");
            String nativePlace = request.getParameter("nativePlace");
            String address = request.getParameter("address");
            String phone = request.getParameter("phone");
            String grade = request.getParameter("grade");
            String isLeagueMemberStr = request.getParameter("isLeagueMember");
            String applicationDateStr = request.getParameter("applicationDate");
            String remarks = request.getParameter("remarks");
            
            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                writeErrorResponse(response, "姓名不能为空");
                return;
            }
            if (idCard == null || idCard.trim().isEmpty()) {
                writeErrorResponse(response, "身份证号不能为空");
                return;
            }
            if (grade == null || grade.trim().isEmpty()) {
                writeErrorResponse(response, "年级不能为空");
                return;
            }
            
            // 解析参数
            Boolean isLeagueMember = Boolean.parseBoolean(isLeagueMemberStr);
            Date applicationDate = null;
            if (applicationDateStr != null && !applicationDateStr.trim().isEmpty()) {
                try {
                    applicationDate = dateFormat.parse(applicationDateStr);
                } catch (ParseException e) {
                    writeErrorResponse(response, "申请日期格式错误");
                    return;
                }
            }
            
            // 创建申请人对象
            PartyApplicant applicant = new PartyApplicant(
                name.trim(), idCard.trim(), nativePlace, address, 
                phone, grade.trim(), isLeagueMember, applicationDate, "待审核"
            );
            
            if (remarks != null && !remarks.trim().isEmpty()) {
                applicant.setRemarks(remarks.trim());
            }
            
            // 保存申请人
            Integer savedId = applicantService.add(applicant);
            if (savedId != null && savedId > 0) {
                // 重新查询保存后的申请人信息
                PartyApplicant savedApplicant = applicantService.getById(savedId);
                writeSuccessResponse(response, savedApplicant, "申请人添加成功");
            } else {
                writeErrorResponse(response, "申请人添加失败");
            }
            
        } catch (Exception e) {
            System.err.println("添加申请人失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "添加申请人失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理积极分子审议
     */
    private void handleActivistReview(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        try {
            // 获取审议参数
            String applicantIdStr = request.getParameter("applicantId");
            String activistDateStr = request.getParameter("activistDate");
            String branchSecretary = request.getParameter("branchSecretary");
            String hasLeagueRecommendationStr = request.getParameter("hasLeagueRecommendation");
            String remarks = request.getParameter("remarks");
            
            // 验证必填字段
            if (applicantIdStr == null || applicantIdStr.trim().isEmpty()) {
                writeErrorResponse(response, "申请人ID不能为空");
                return;
            }
            if (activistDateStr == null || activistDateStr.trim().isEmpty()) {
                writeErrorResponse(response, "确认积极分子时间不能为空");
                return;
            }
            if (branchSecretary == null || branchSecretary.trim().isEmpty()) {
                writeErrorResponse(response, "支部书记不能为空");
                return;
            }
            
            // 解析参数
            Integer applicantId;
            try {
                applicantId = Integer.parseInt(applicantIdStr);
            } catch (NumberFormatException e) {
                writeErrorResponse(response, "申请人ID格式错误");
                return;
            }
            
            Date activistDate;
            try {
                activistDate = dateFormat.parse(activistDateStr);
            } catch (ParseException e) {
                writeErrorResponse(response, "确认积极分子时间格式错误");
                return;
            }
            
            Boolean hasLeagueRecommendation = Boolean.parseBoolean(hasLeagueRecommendationStr);
            
            // 创建审议DTO
            ActivistReviewDTO reviewDTO = new ActivistReviewDTO(
                applicantId, activistDate, branchSecretary.trim(), 
                hasLeagueRecommendation, remarks
            );
            
            // 执行审议转换
            PartyActivist activist = reviewService.convertToActivist(reviewDTO);
            if (activist != null) {
                writeSuccessResponse(response, activist, "积极分子审议成功，已转为入党积极分子");
            } else {
                writeErrorResponse(response, "积极分子审议失败");
            }
            
        } catch (Exception e) {
            System.err.println("积极分子审议失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "积极分子审议失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理更新申请人
     */
    private void handleUpdate(HttpServletRequest request, HttpServletResponse response, Integer id) 
            throws IOException {
        
        try {
            // 获取现有申请人
            PartyApplicant existingApplicant = applicantService.getById(id);
            if (existingApplicant == null) {
                writeErrorResponse(response, "未找到指定的申请人");
                return;
            }
            
            // 更新字段（这里简化处理，实际项目中应该更完善）
            String name = request.getParameter("name");
            if (name != null && !name.trim().isEmpty()) {
                existingApplicant.setName(name.trim());
            }
            
            String remarks = request.getParameter("remarks");
            if (remarks != null) {
                existingApplicant.setRemarks(remarks.trim());
            }
            
            // 保存更新
            boolean updated = applicantService.update(existingApplicant);
            if (updated) {
                // 重新查询更新后的申请人信息
                PartyApplicant updatedApplicant = applicantService.getById(id);
                writeSuccessResponse(response, updatedApplicant, "申请人更新成功");
            } else {
                writeErrorResponse(response, "申请人更新失败");
            }
            
        } catch (Exception e) {
            System.err.println("更新申请人失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "更新申请人失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理删除申请人
     */
    private void handleDelete(HttpServletRequest request, HttpServletResponse response, Integer id) 
            throws IOException {
        
        try {
            boolean deleted = applicantService.deleteById(id);
            if (deleted) {
                writeSuccessResponse(response, null, "申请人删除成功");
            } else {
                writeErrorResponse(response, "申请人删除失败");
            }
        } catch (Exception e) {
            System.err.println("删除申请人失败: " + e.getMessage());
            e.printStackTrace();
            writeErrorResponse(response, "删除申请人失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取整数参数
     */
    private int getIntParameter(HttpServletRequest request, String name, int defaultValue) {
        String value = request.getParameter(name);
        if (value != null && !value.trim().isEmpty()) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 写入成功响应
     */
    private void writeSuccessResponse(HttpServletResponse response, Object data, String message) 
            throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String json = JsonUtil.createSuccessResponse(data, message);
        PrintWriter out = response.getWriter();
        out.write(json);
        out.flush();
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        
        String json = JsonUtil.createErrorResponse(message);
        PrintWriter out = response.getWriter();
        out.write(json);
        out.flush();
    }
}
