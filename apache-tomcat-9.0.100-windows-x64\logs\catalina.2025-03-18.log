18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/9.0.100
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Feb 13 2025 11:29:56 UTC
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      9.0.100.0
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Windows 8.1
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           6.3
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              x86
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     D:\软件安装文件夹\中国共产党党内统计系统\ccpweb3\env\openjdk8\jre
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_41-b04
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     F:\javaweb\apache-tomcat-9.0.100-windows-x64
18-Mar-2025 20:12:24.108 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     F:\javaweb\apache-tomcat-9.0.100-windows-x64
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=F:\javaweb\apache-tomcat-9.0.100-windows-x64\conf\logging.properties
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dsun.io.useCanonCaches=false
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=F:\javaweb\apache-tomcat-9.0.100-windows-x64
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=F:\javaweb\apache-tomcat-9.0.100-windows-x64
18-Mar-2025 20:12:24.112 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=F:\javaweb\apache-tomcat-9.0.100-windows-x64\temp
18-Mar-2025 20:12:24.116 警告 [main] org.apache.catalina.core.AprLifecycleListener.init 基于APR的本地库加载失败.错误报告为[F:\javaweb\apache-tomcat-9.0.100-windows-x64\bin\tcnative-1.dll: Can't load AMD 64-bit .dll on a IA 32-bit platform]
	java.lang.UnsatisfiedLinkError: F:\javaweb\apache-tomcat-9.0.100-windows-x64\bin\tcnative-1.dll: Can't load AMD 64-bit .dll on a IA 32-bit platform
		at java.lang.ClassLoader$NativeLibrary.load(Native Method)
		at java.lang.ClassLoader.loadLibrary0(ClassLoader.java:1937)
		at java.lang.ClassLoader.loadLibrary(ClassLoader.java:1822)
		at java.lang.Runtime.load0(Runtime.java:809)
		at java.lang.System.load(System.java:1086)
		at org.apache.tomcat.jni.Library.<init>(Library.java:49)
		at org.apache.tomcat.jni.Library.initialize(Library.java:260)
		at org.apache.catalina.core.AprLifecycleListener.init(AprLifecycleListener.java:212)
		at org.apache.catalina.core.AprLifecycleListener.lifecycleEvent(AprLifecycleListener.java:143)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:389)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:121)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:690)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:713)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:497)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:472)
18-Mar-2025 20:12:24.308 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
18-Mar-2025 20:12:24.325 信息 [main] org.apache.catalina.startup.Catalina.load 服务器在[403]毫秒内初始化
18-Mar-2025 20:12:24.350 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
18-Mar-2025 20:12:24.350 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/9.0.100]
18-Mar-2025 20:12:24.363 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\docs]
18-Mar-2025 20:12:25.371 警告 [main] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom 使用[SHA1PRNG]创建会话ID生成的SecureRandom实例花费了[808]毫秒。
18-Mar-2025 20:12:25.400 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\docs]的部署已在[1,037]毫秒内完成
18-Mar-2025 20:12:25.400 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\examples]
18-Mar-2025 20:12:26.122 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\examples]的部署已在[722]毫秒内完成
18-Mar-2025 20:12:26.127 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\host-manager]
18-Mar-2025 20:12:26.234 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\host-manager]的部署已在[112]毫秒内完成
18-Mar-2025 20:12:26.234 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\manager]
18-Mar-2025 20:12:26.352 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\manager]的部署已在[116]毫秒内完成
18-Mar-2025 20:12:26.352 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\ROOT]
18-Mar-2025 20:12:26.447 信息 [main] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[F:\javaweb\apache-tomcat-9.0.100-windows-x64\webapps\ROOT]的部署已在[93]毫秒内完成
18-Mar-2025 20:12:26.460 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
18-Mar-2025 20:12:26.522 信息 [main] org.apache.catalina.startup.Catalina.start [2196]毫秒后服务器启动
