<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.100) - The Context Container</title><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Context Container</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a><ol><li><a href="#Parallel_deployment">Parallel deployment</a></li><li><a href="#Naming">Naming</a></li><li><a href="#Defining_a_context">Defining a context</a></li></ol></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementation">Standard Implementation</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a><ol><li><a href="#Logging">Logging</a></li><li><a href="#Access_Logs">Access Logs</a></li><li><a href="#Automatic_Context_Configuration">Automatic Context Configuration</a></li><li><a href="#Context_Parameters">Context Parameters</a></li><li><a href="#Environment_Entries">Environment Entries</a></li><li><a href="#Lifecycle_Listeners">Lifecycle Listeners</a></li><li><a href="#Request_Filters">Request Filters</a></li><li><a href="#Resource_Definitions">Resource Definitions</a></li><li><a href="#Resource_Links">Resource Links</a></li><li><a href="#Transaction">Transaction</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

    <p><em>
    The description below uses the variable name $CATALINA_BASE to refer the
    base directory against which most relative paths are resolved. If you have
    not configured Tomcat for multiple instances by setting a CATALINA_BASE
    directory, then $CATALINA_BASE will be set to the value of $CATALINA_HOME,
    the directory into which you have installed Tomcat.
    </em></p>

  <p>The <strong>Context</strong> element represents a <em>web
  application</em>, which is run within a particular virtual host.
  Each web application is based on a <em>Web Application Archive</em>
  (WAR) file, or a corresponding directory containing the corresponding
  unpacked contents, as described in the Servlet Specification (version
  2.2 or later).  For more information about web application archives,
  you can download the
  <a href="https://cwiki.apache.org/confluence/display/TOMCAT/Specifications">Servlet
  Specification</a>, and review the Tomcat
  <a href="../appdev/index.html">Application Developer's Guide</a>.</p>

  <p>The web application used to process each HTTP request is selected
  by Catalina based on matching the longest possible prefix of the
  Request URI against the <em>context path</em> of each defined Context.
  Once selected, that Context will select an appropriate servlet to
  process the incoming request, according to the servlet mappings defined
  by the web application deployment.</p>

  <p>You may define as many <strong>Context</strong> elements as you
  wish.  Each such Context MUST have a unique context name within a virtual
  host. The context path does not need to be unique (see <em>parallel
  deployment</em> below). In addition, a Context must be present with a
  context path equal to
  a zero-length string.  This Context becomes the <em>default</em>
  web application for this virtual host, and is used to process all
  requests that do not match any other Context's context path.</p>

  <div class="subsection"><h4 id="Parallel_deployment">Parallel deployment</h4><div class="text">
  <p><b>You may deploy multiple versions of a web application with the same
  context path at the same time.</b> The rules used to match requests to a
  context version are as follows:
  </p>
  <ul>
  <li>If no session information is present in the request, use the latest
  version.</li>
  <li>If session information is present in the request, check the session
  manager of each version for a matching session and if one is found, use that
  version.</li>
  <li>If session information is present in the request but no matching session
  can be found, use the latest version.</li>
  </ul>
  <p>The <a href="host.html">Host</a> may be configured (via the
  <code>undeployOldVersions</code>) to remove old versions deployed in this way
  once they are no longer in use.</p>
  </div></div>

  <div class="subsection"><h4 id="Naming">Naming</h4><div class="text">
  <p>When <code>autoDeploy</code> or <code>deployOnStartup</code> operations
  are performed by a Host, the name and context path of the web application are
  derived from the name(s) of the file(s) that define(s) the web application.
  Consequently, the context path <strong>may not</strong> be defined in a
  <code>META-INF/context.xml</code> embedded in the application and there is a
  close relationship between the <em>context name</em>, <em>context path</em>,
  <em>context version</em> and the <em>base file name</em> (the name minus any
  <code>.war</code> or <code>.xml</code> extension) of the file.</p>

  <p>If no version is specified then the <em>context name</em> is always the
  same as the <em>context path</em>. If the <em>context path</em> is the empty
  string then the <em>base name</em> will be ROOT (always in upper case)
  otherwise the <em>base name</em> will be the <em>context path</em> with the
  leading '/' removed and any remaining '/' characters replaced with '#'.</p>

  <p>If a version is specified then the <em>context path</em> remains unchanged
  and both the <em>context name</em> and the <em>base name</em> have the string
  '##' appended to them followed by the version identifier.</p>

  <p>Some examples of these naming conventions are given below.</p>

  <table class="detail-table">
    <tr>
      <th>Context Path</th>
      <th>Context Version</th>
      <th>Context Name</th>
      <th>Base File Name</th>
      <th>Example File Names (.xml, .war &amp; directory)</th>
    </tr>
    <tr>
      <td>/foo</td>
      <td><i>None</i></td>
      <td>/foo</td>
      <td>foo</td>
      <td>foo.xml, foo.war, foo</td>
    </tr>
    <tr>
      <td>/foo/bar</td>
      <td><i>None</i></td>
      <td>/foo/bar</td>
      <td>foo#bar</td>
      <td>foo#bar.xml, foo#bar.war, foo#bar</td>
    </tr>
    <tr>
      <td><i>Empty String</i></td>
      <td><i>None</i></td>
      <td><i>Empty String</i></td>
      <td>ROOT</td>
      <td>ROOT.xml, ROOT.war, ROOT</td>
    </tr>
    <tr>
      <td>/foo</td>
      <td>42</td>
      <td>/foo##42</td>
      <td>foo##42</td>
      <td>foo##42.xml, foo##42.war, foo##42</td>
    </tr>
    <tr>
      <td>/foo/bar</td>
      <td>42</td>
      <td>/foo/bar##42</td>
      <td>foo#bar##42</td>
      <td>foo#bar##42.xml, foo#bar##42.war, foo#bar##42</td>
    </tr>
    <tr>
      <td><i>Empty String</i></td>
      <td>42</td>
      <td>##42</td>
      <td>ROOT##42</td>
      <td>ROOT##42.xml, ROOT##42.war, ROOT##42</td>
    </tr>
  </table>

  <p>The version component is treated as a <code>String</code> both for
  performance reasons and to allow flexibility in versioning schemes. String
  comparisons are used to determine version order. If version is not specified,
  it is treated as the empty string.
  Therefore,
  <code>foo.war</code> will be treated as an earlier version than
  <code>foo##11.war</code> and
  <code>foo##11.war</code> will be treated as an earlier version than
  <code>foo##2.war</code>. If using a purely numerical versioning scheme it is
  recommended that zero padding is used so that <code>foo##002.war</code> is
  treated as an earlier version than <code>foo##011.war</code>.
  </p>

  <p>If you want to deploy a WAR file or a directory using a context path that
  is not related to the base file name then one of the following options must
  be used to prevent double-deployment:
  </p>
  <ul>
  <li>Disable autoDeploy and deployOnStartup and define all
  <strong>Context</strong>s in server.xml</li>
  <li>Locate the WAR and/or directory outside of the Host's
      <code>appBase</code> and use a context.xml file with a
      <code>docBase</code> attribute to define it.</li>
  </ul>
  </div></div>

  <div class="subsection"><h4 id="Defining_a_context">Defining a context</h4><div class="text">
  <p><b>It is NOT recommended to place &lt;Context&gt; elements directly in the
  server.xml file.</b> This is because it makes modifying the
  <strong>Context</strong> configuration more invasive since the main
  <code>conf/server.xml</code> file cannot be reloaded without restarting
  Tomcat. Default <strong>Context</strong> elements (see below) will also
  <strong>overwrite</strong> the configuration of any &lt;Context&gt; elements
  placed directly in server.xml. To prevent this, the <code>override</code>
  attribute of the &lt;Context&gt; element defined in server.xml should be set
  to <code>true</code>.</p>

  <p>Individual <strong>Context</strong> elements may be explicitly defined:
  </p>
  <ul>
  <li>In an individual file at <code>/META-INF/context.xml</code> inside the
  application files. Optionally (based on the Host's copyXML attribute)
  this may be copied to
  <code>$CATALINA_BASE/conf/[enginename]/[hostname]/</code> and renamed to
  application's base file name plus a ".xml" extension.</li>
  <li>In individual files (with a ".xml" extension) in the
  <code>$CATALINA_BASE/conf/[enginename]/[hostname]/</code> directory.
  The context path and version will be derived from the base name of the file
  (the file name less the .xml extension). This file will always take precedence
  over any context.xml file packaged in the web application's META-INF
  directory.</li>
  <li>Inside a <a href="host.html">Host</a> element in the main
  <code>conf/server.xml</code>.</li>
  </ul>

  <p>Default <strong>Context</strong> elements may be defined that apply to
  multiple web applications. Configuration for an individual web application
  will override anything configured in one of these defaults. Any nested
  elements, e.g. &lt;Resource&gt; elements, that are defined in a default
  <strong>Context</strong> will be created once for each
  <strong>Context</strong> to which the default applies. They will <b>not</b> be
  shared between <strong>Context</strong> elements.
  </p>
  <ul>
  <li>In the <code>$CATALINA_BASE/conf/context.xml</code> file:
  the Context element information will be loaded by all web applications.</li>
  <li>In the
  <code>$CATALINA_BASE/conf/[enginename]/[hostname]/context.xml.default</code>
  file: the Context element information will be loaded by all web applications
  of that host.</li>
  </ul>

  <p>With the exception of server.xml, files that define <strong>Context
  </strong> elements may only define a single <strong>Context</strong> element.
  </p>

  <p>In addition to explicitly specified Context elements, there are
  several techniques by which Context elements can be created automatically
  for you.  See <a href="host.html#Automatic_Application_Deployment">
  Automatic Application Deployment</a> and
  <a href="host.html#User_Web_Applications">User Web Applications</a>
  for more information.</p>

  <p>To define multiple contexts that use a single WAR file or directory,
  use one of the options described in the <a href="#Naming">Naming</a>
  section above for creating a <strong>Context</strong> that has a path
  that is not related to the base file name.</p>
  </div></div>
</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>Context</strong>
    support the following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_allowCasualMultipartParsing"><td><code class="attributeName">allowCasualMultipartParsing</code></td><td>
        <p>Set to <code>true</code> if Tomcat should automatically parse
        multipart/form-data request bodies when HttpServletRequest.getPart*
        or HttpServletRequest.getParameter* is called, even when the
        target servlet isn't marked with the @MultipartConfig annotation
        (See Servlet Specification 3.0, Section 3.2 for details).
        Note that any setting other than <code>false</code> causes Tomcat
        to behave in a way that is not technically spec-compliant.
        The default is <code>false</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_allowMultipleLeadingForwardSlashInPath"><td><code class="attributeName">allowMultipleLeadingForwardSlashInPath</code></td><td>
        <p>Tomcat normalises sequences of multiple <code>/</code> characters in
        a URI to a single <code>/</code>. This is for consistency with the
        behaviour of file systems as URIs are often translated to file system
        paths. As a result, the return value of
        <code>HttpServletRequest#getContextPath()</code> is expected to start
        with multiple <code>/</code> characters for some URIs. This will cause
        problems if this value is used directly with
        <code>HttpServletResponse#sendRedirect()</code> as redirect paths that
        start with <code>//</code> are treated as protocol relative redirects.
        To avoid potential issues, Tomcat will collapse multiple leading
        <code>/</code> characters at the start of the return value for
        <code>HttpServletRequest#getContextPath()</code> to a single
        <code>/</code>. This attribute has a default value of <code>false</code>
        which enables the collapsing of multiple <code>/</code> characters. To
        disable this behaviour, set this attribute to <code>true</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_altDDName"><td><code class="attributeName">altDDName</code></td><td>
        <p>The absolute path to the alternative deployment descriptor for this
        context. This overrides the default deployment descriptor located at
        <code>/WEB-INF/web.xml</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_backgroundProcessorDelay"><td><code class="attributeName">backgroundProcessorDelay</code></td><td>
        <p>This value represents the delay in seconds between the
        invocation of the backgroundProcess method on this context and
        its child containers, including all wrappers.
        Child containers will not be invoked if their delay value is not
        negative (which would mean they are using their own processing
        thread). Setting this to a positive value will cause
        a thread to be spawn. After waiting the specified amount of time,
        the thread will invoke the backgroundProcess method on this host
        and all its child containers. A context will use background
        processing to perform session expiration and class monitoring for
        reloading. If not specified, the default value for this attribute is
        -1, which means the context will rely on the background processing
        thread of its parent host.</p>
      </td></tr><tr id="Attributes_Common Attributes_className"><td><code class="attributeName">className</code></td><td>
        <p>Java class name of the implementation to use.  This class must
        implement the <code>org.apache.catalina.Context</code> interface.
        If not specified, the standard value (defined below) will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_containerSciFilter"><td><code class="attributeName">containerSciFilter</code></td><td>
        <p>The regular expression that specifies which container provided SCIs
        should be filtered out and not used for this context. Matching uses
        <code>java.util.regex.Matcher.find()</code> so the regular expression
        only has to match a sub-string of the fully qualified class name of the
        container provided SCI for it to be filtered out. If not specified,
        no filtering will be applied.</p>
      </td></tr><tr id="Attributes_Common Attributes_cookies"><td><code class="attributeName">cookies</code></td><td>
        <p>Set to <code>true</code> if you want cookies to be used for
        session identifier communication if supported by the client (this
        is the default).  Set to <code>false</code> if you want to disable
        the use of cookies for session identifier communication, and rely
        only on URL rewriting by the application.</p>
      </td></tr><tr id="Attributes_Common Attributes_createUploadTargets"><td><code class="attributeName">createUploadTargets</code></td><td>
        <p>Set to <code>true</code> if Tomcat should attempt to create the
        temporary upload location specified in the <code>MultipartConfig</code>
        for a Servlet if the location does not already exist. If not specified,
        the default value of <code>false</code> will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_crossContext"><td><code class="attributeName">crossContext</code></td><td>
        <p>Set to <code>true</code> if you want calls within this application
        to <code>ServletContext.getContext()</code> to successfully return a
        request dispatcher for other web applications running on this virtual
        host.  Set to <code>false</code> (the default) in security
        conscious environments, to make <code>getContext()</code> always
        return <code>null</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_dispatchersUseEncodedPaths"><td><code class="attributeName">dispatchersUseEncodedPaths</code></td><td>
        <p>Controls whether paths used in calls to obtain a request dispatcher
        ares expected to be encoded. This affects both how Tomcat handles calls
        to obtain a request dispatcher as well as how Tomcat generates paths
        used to obtain request dispatchers internally. If not specified, the
        default value of <code>true</code> is used. When encoding/decoding paths
        for a request dispatcher, UTF-8 is always used.</p>
      </td></tr><tr id="Attributes_Common Attributes_docBase"><td><strong><code class="attributeName">docBase</code></strong></td><td>
        <p>The <em>Document Base</em> (also known as the <em>Context
        Root</em>) directory for this web application, or the pathname
        to the web application archive file (if this web application is
        being executed directly from the WAR file). You may specify
        an absolute pathname for this directory or WAR file, or a pathname
        that is relative to the <code>appBase</code> directory of the
        owning <a href="host.html">Host</a>.</p>
        <p>The value of this field must not be set unless the Context element is
        defined in server.xml or the <code>docBase</code> is not located under
        the <a href="host.html">Host</a>'s <code>appBase</code>.</p>
        <p>If a symbolic link is used for <code>docBase</code> then changes to
        the symbolic link will only be effective after a Tomcat restart or
        by undeploying and redeploying the context. A context reload is not
        sufficient.</p>
      </td></tr><tr id="Attributes_Common Attributes_encodedReverseSolidusHandling"><td><code class="attributeName">encodedReverseSolidusHandling</code></td><td>
        <p>When set to <code>reject</code>, an attempt to obtain a
        <code>RequestDispatcher</code> for a path containing a <code>%5c</code>
        sequence will trigger an <code>IllegalArgumentException</code>. When set
        to <code>decode</code>, any <code>%5c</code> sequence in the path used
        to obtain a <code>RequestDispatcher</code> will have that sequence
        decoded to <code>\</code> (and then normalized to <code>/</code>) before
        the <code>RequestDispatcher</code> is created. When set to
        <code>passthrough</code>, any <code>%5c</code> sequence in the path used
        to obtain a <code>RequestDispatcher</code> will remain unchanged.</p>
        <p>If <code>passthrough</code> is used then it is the application's
        resposibility to perform any further <code>%nn</code> decoding required.
        Any <code>%25</code> sequences (encoded <code>%</code>) in the path will
        also be processed with the <code>%25</code> sequence unchanged
        to avoid potential corruption and/or decoding failure when the path is
        subsequently <code>%nn</code> decoded by the application.</p>
        <p>If not specified, the default value is <code>decode</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_encodedSolidusHandling"><td><code class="attributeName">encodedSolidusHandling</code></td><td>
        <p>When set to <code>reject</code>, an attempt to obtain a
        <code>RequestDispatcher</code> for a path containing a <code>%2f</code>
        sequence will trigger an <code>IllegalArgumentException</code>. When set
        to <code>decode</code>, any <code>%2f</code> sequence in the path used
        to obtain a <code>RequestDispatcher</code> will have that sequence
        decoded to <code>/</code> before the <code>RequestDispatcher</code> is
        created. When set to <code>passthrough</code>, any <code>%2f</code>
        sequence in the path used to obtain a <code>RequestDispatcher</code>
        will remain unchanged.</p>
        <p>If <code>passthrough</code> is used then it is the application's
        resposibility to perform any further <code>%nn</code> decoding required.
        Any <code>%25</code> sequences (encoded <code>%</code>) in the path will
        also be processed with the <code>%25</code> sequence unchanged
        to avoid potential corruption and/or decoding failure when the path is
        subsequently <code>%nn</code> decoded by the application.</p>
        <p>If not specified, the default value is <code>decode</code>. This
        default will change to <code>reject</code> (to align with the
        <strong>Connector</strong>) in Tomcat 12.</p>
      </td></tr><tr id="Attributes_Common Attributes_failCtxIfServletStartFails"><td><code class="attributeName">failCtxIfServletStartFails</code></td><td>
        <p>Set to <code>true</code> to have the context fail its startup if any
        servlet that has load-on-startup &gt;=0 fails its own startup.</p>
        <p>If not specified, the attribute of the same name in the parent Host
        configuration is used if specified. Otherwise the default value of
        <code>false</code> is used.</p>
      </td></tr><tr id="Attributes_Common Attributes_fireRequestListenersOnForwards"><td><code class="attributeName">fireRequestListenersOnForwards</code></td><td>
        <p>Set to <code>true</code> to fire any configured
        ServletRequestListeners  when Tomcat forwards a request. This is
        primarily of use to users of CDI frameworks that use
        ServletRequestListeners to configure the necessary environment for a
        request. If not specified, the default value of <code>false</code> is
        used.</p>
      </td></tr><tr id="Attributes_Common Attributes_logEffectiveWebXml"><td><code class="attributeName">logEffectiveWebXml</code></td><td>
        <p>Set to <code>true</code> if you want the effective web.xml used for a
        web application to be logged (at INFO level) when the application
        starts. The effective web.xml is the result of combining the
        application's web.xml with any defaults configured by Tomcat and any
        web-fragment.xml files and annotations discovered. If not specified, the
        default value of <code>false</code> is used.</p>
      </td></tr><tr id="Attributes_Common Attributes_mapperContextRootRedirectEnabled"><td><code class="attributeName">mapperContextRootRedirectEnabled</code></td><td>
        <p>If enabled, requests for a web application context root will be
        redirected (adding a trailing slash) if necessary by the Mapper rather
        than the default Servlet. This is more efficient but there are security
        side effects. First, the existance of a web application or a directory
        may be confirmed even though the user does not have access to that
        directory. Secondly, any Valves and/or Filters - including those
        providing security functionality - will not have an opportunity to
        process the request. If not specified, the default value of
        <code>true</code> is used.</p>
      </td></tr><tr id="Attributes_Common Attributes_mapperDirectoryRedirectEnabled"><td><code class="attributeName">mapperDirectoryRedirectEnabled</code></td><td>
        <p>If enabled, requests for a web application directory will be
        redirected (adding a trailing slash) if necessary by the Mapper rather
        than the default Servlet. This is more efficient but there are security
        side effects. First, the existance of a web application or a directory
        may be confirmed even though the user does not have access to that
        directory. Secondly, any Valves and/or Filters - including those
        providing security functionality - will not have an opportunity to
        process the request. If not specified, the default value of
        <code>false</code> is used.</p>
      </td></tr><tr id="Attributes_Common Attributes_override"><td><code class="attributeName">override</code></td><td>
        <p>Set to <code>true</code> to ignore any settings in both the global
        or <a href="host.html">Host</a> default contexts.  By default, settings
        from a default context will be used but may be overridden by a setting
        the same attribute explicitly for the Context.</p>
      </td></tr><tr id="Attributes_Common Attributes_parallelAnnotationScanning"><td><code class="attributeName">parallelAnnotationScanning</code></td><td>
        <p>When set to <code>true</code> annotation scanning will be performed
        using the utility executor. It will allow processing scanning in
        parallel which may improve deployment type at the expense of higher
        server load. If not specified, the default of <code>false</code> is
        used.</p>
      </td></tr><tr id="Attributes_Common Attributes_path"><td><code class="attributeName">path</code></td><td>
        <p>The <em>context path</em> of this web application, which is
        matched against the beginning of each request URI to select the
        appropriate web application for processing.  All of the context paths
        within a particular <a href="host.html">Host</a> must be unique.
        If you specify a context path of an empty string (""), you are
        defining the <em>default</em> web application for this Host, which
        will process all requests not assigned to other Contexts.</p>
        <p>This attribute must only be used when statically defining a Context
        in server.xml. In all other circumstances, the path will be inferred
        from the filenames used for either the .xml context file or the
        <code>docBase</code>.
        </p>
        <p>Even when statically defining a Context in server.xml, this attribute
        must not be set unless either the <code>docBase</code> is not located
        under the <a href="host.html">Host</a>'s <code>appBase</code> or
        both <code>deployOnStartup</code> and <code>autoDeploy</code> are
        <code>false</code>. If this rule is not followed, double deployment is
        likely to result.</p>
      </td></tr><tr id="Attributes_Common Attributes_preemptiveAuthentication"><td><code class="attributeName">preemptiveAuthentication</code></td><td>
        <p>When set to <code>true</code> and the user presents credentials for a
        resource that is not protected by a security constraint, if the
        authenticator supports preemptive authentication (the standard
        authenticators provided with Tomcat do) then the user' credentials
        will be processed. If not specified, the default of <code>false</code> is
        used.
        </p>
      </td></tr><tr id="Attributes_Common Attributes_privileged"><td><code class="attributeName">privileged</code></td><td>
        <p>Set to <code>true</code> to allow this context to use container
        servlets, like the manager servlet. Use of the <code>privileged</code>
        attribute will change the context's parent class loader to be the
        <em>Server</em> class loader rather than the <em>Shared</em> class
        loader. Note that in a default installation, the <em>Common</em> class
        loader is used for both the <em>Server</em> and the <em>Shared</em>
        class loaders.</p>
      </td></tr><tr id="Attributes_Common Attributes_reloadable"><td><code class="attributeName">reloadable</code></td><td>
        <p>Set to <code>true</code> if you want Catalina to monitor classes in
        <code>/WEB-INF/classes/</code> and <code>/WEB-INF/lib</code> for
        changes, and automatically reload the web application if a change
        is detected.  This feature is very useful during application
        development, but it requires significant runtime overhead and is
        not recommended for use on deployed production applications.  That's
        why the default setting for this attribute is <i>false</i>.  You
        can use the <a href="../manager-howto.html">Manager</a> web
        application, however, to trigger reloads of deployed applications
        on demand.</p>
      </td></tr><tr id="Attributes_Common Attributes_resourceOnlyServlets"><td><code class="attributeName">resourceOnlyServlets</code></td><td>
        <p>Comma separated list of Servlet names (as used in
        <code>/WEB-INF/web.xml</code>) that expect a resource to be present.
        Ensures that welcome files associated with Servlets that expect a
        resource to be present (such as the JSP Servlet) are not used when there
        is no resource present. This prevents issues caused by the clarification
        of welcome file mapping in section 10.10 of the Servlet 3.0
        specification. If the
        <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code>
        <a href="systemprops.html">system property</a> is set to
        <code>true</code>, the default value of this attribute will be the empty
        string, else the default value will be <code>jsp</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_sendRedirectBody"><td><code class="attributeName">sendRedirectBody</code></td><td>
        <p>If <code>true</code>, redirect responses will include a short
        response body that includes details of the redirect as recommended by
        RFC 2616. This is disabled by default since including a response body
        may cause problems for some application component such as compression
        filters.</p>
      </td></tr><tr id="Attributes_Common Attributes_sessionCookieDomain"><td><code class="attributeName">sessionCookieDomain</code></td><td>
        <p>The domain to be used for all session cookies created for this
        context. If set, this overrides any domain set by the web application.
        If not set, the value specified by the web application, if any, will be
        used.</p>
      </td></tr><tr id="Attributes_Common Attributes_sessionCookieName"><td><code class="attributeName">sessionCookieName</code></td><td>
        <p>The name to be used for all session cookies created for this
        context. If set, this overrides any name set by the web application.
        If not set, the value specified by the web application, if any, will be
        used, or the name <code>JSESSIONID</code> if the web application does
        not explicitly set one.</p>
      </td></tr><tr id="Attributes_Common Attributes_sessionCookiePath"><td><code class="attributeName">sessionCookiePath</code></td><td>
        <p>The path to be used for all session cookies created for this
        context. If set, this overrides any path set by the web application.
        If not set, the value specified by the web application will be used, or
        the context path used if the web application does not explicitly set
        one. To configure all web application to use an empty path (this can be
        useful for portlet specification implementations) set this attribute to
        <code>/</code> in the global <code>CATALINA_BASE/conf/context.xml</code>
        file.</p>
        <p>Note: Once one web application using
        <code>sessionCookiePath="/"</code> obtains a session, all
        subsequent sessions for any other web application in the same host also
        configured with <code>sessionCookiePath="/"</code> will always
        use the same session ID. This holds even if the session is invalidated
        and a new one created. This makes session fixation protection more
        difficult and requires custom, Tomcat specific code to change the
        session ID shared by the multiple applications.</p>
      </td></tr><tr id="Attributes_Common Attributes_sessionCookiePathUsesTrailingSlash"><td><code class="attributeName">sessionCookiePathUsesTrailingSlash</code></td><td>
        <p>Some browsers, such as Internet Explorer, Safari and Edge, will send
        a session cookie for a context with a path of <code>/foo</code> with a
        request to <code>/foobar</code> in violation of RFC6265. This could
        expose a session ID from an application deployed at <code>/foo</code> to
        an application deployed at <code>/foobar</code>. If the application
        deployed at <code>/foobar</code> is untrusted, this could create a
        security risk. However, it should be noted that RFC 6265, section 8.5
        makes clear that path alone should not be view as sufficient to prevent
        untrusted applications accessing cookies from other applications. To
        mitigate this risk, this attribute may be set to <code>true</code> and
        Tomcat will add a trailing slash to the path associated with the session
        cookie so, in the above example, the cookie path becomes /foo/. However,
        with a cookie path of /foo/, browsers will no longer send the cookie
        with a request to /foo. This should not be a problem unless there is a
        servlet mapped to /*. In this case this attribute will need to be set to
        <code>false</code> to disable this feature. The default value for this
        attribute is <code>false</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_swallowAbortedUploads"><td><code class="attributeName">swallowAbortedUploads</code></td><td>
        <p>Set to <code>false</code> if Tomcat should <b>not</b> read any
        additional request body data for aborted uploads and instead abort the
        client connection. This setting is used in the following situations:
        </p>
        <ul>
        <li>the size of the request body is larger than the
            <code>maxPostSize</code> configured in the connector</li>
        <li>the size limit of a MultiPart upload is reached</li>
        <li>the servlet sets the response status to 413 (Request Entity Too
            Large) </li>
        </ul>
        <p>
        Not reading the additional data will free the request processing thread
        more quickly. Unfortunately most HTTP clients will not read the response
        if they cannot write the full request.</p>
        <p>The default is <code>true</code>, so additional data will be
        read.</p>
        <p>Note if an error occurs during the request processing that triggers
        a 5xx response, any unread request data will always be ignored and the
        client connection will be closed once the error response has been
        written.</p>
      </td></tr><tr id="Attributes_Common Attributes_swallowOutput"><td><code class="attributeName">swallowOutput</code></td><td>
        <p>If the value of this flag is <code>true</code>, the bytes output to
        System.out and System.err by the web application will be redirected to
        the web application logger. If not specified, the default value
        of the flag is <code>false</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_tldValidation"><td><code class="attributeName">tldValidation</code></td><td>
        <p>If the value of this flag is <code>true</code>, the TLD files
        will be XML validated on context startup. If the
        <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code>
        <a href="systemprops.html">system property</a> is set to
        <code>true</code>, the default value of this attribute will be
        <code>true</code>, else the default value will be <code>false</code>.
        Setting this attribute to <code>true</code> will incur a performance
        penalty.</p>
      </td></tr><tr id="Attributes_Common Attributes_useBloomFilterForArchives"><td><code class="attributeName">useBloomFilterForArchives</code></td><td>
        <p>DEPRECATED: If this is <code>true</code> then a bloom filter will be
        used to speed up archive lookups. This can be beneficial to the deployment
        speed to web applications that contain very large amount of JARs.</p>
        <p>If not specified, the default value of <code>false</code> will be
        used.</p>
        <p>This value can be overridden by archiveIndexStrategy in
        <a href="resources.html">Resources</a></p>
      </td></tr><tr id="Attributes_Common Attributes_useHttpOnly"><td><code class="attributeName">useHttpOnly</code></td><td>
       <p>Should the HttpOnly flag be set on session cookies to prevent client
          side script from accessing the session ID? Defaults to
          <code>true</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_useRelativeRedirects"><td><code class="attributeName">useRelativeRedirects</code></td><td>
        <p>Controls whether HTTP 1.1 and later location headers generated by a
        call to
        <code>javax.servlet.http.HttpServletResponse#sendRedirect(String)</code>
        will use relative or absolute redirects. Relative redirects are more
        efficient but may not work with reverse proxies that change the context
        path. It should be noted that it is not recommended to use a reverse
        proxy to change the context path because of the multiple issues it
        creates. Absolute redirects should work with reverse proxies that change
        the context path but may cause issues with the
        <code>org.apache.catalina.filters.RemoteIpFilter</code> if the filter is
         changing the scheme and/or port. If the
        <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code>
        <a href="systemprops.html">system property</a> is set to
        <code>true</code>, the default value of this attribute will be
        <code>false</code>, else the default value will be <code>true</code>.
        </p>
      </td></tr><tr id="Attributes_Common Attributes_validateClientProvidedNewSessionId"><td><code class="attributeName">validateClientProvidedNewSessionId</code></td><td>
        <p>When a client provides the ID for a new session, this attribute
        controls whether that ID is validated. The only use case for using a
        client provided session ID is to have a common session ID across
        multiple web applications. Therefore, any client provided session ID
        should already exist in another web application. If this check is
        enabled, the client provided session ID will only be used if the session
        ID exists in at least one other web application for the current host.
        Note that the following additional tests are always applied,
        irrespective of this setting:</p>
        <ul>
          <li>The session ID is provided by a cookie</li>
          <li>The session cookie has a path of {@code /}</li>
        </ul>
        <p>If not specified, the default value of <code>true</code> will be
        used.</p>
      </td></tr><tr id="Attributes_Common Attributes_wrapperClass"><td><code class="attributeName">wrapperClass</code></td><td>
        <p>Java class name of the <code>org.apache.catalina.Wrapper</code>
        implementation class that will be used for servlets managed by this
        Context.  If not specified, a standard default value will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_xmlBlockExternal"><td><code class="attributeName">xmlBlockExternal</code></td><td>
        <p>If the value of this flag is <code>true</code>, the parsing of
        <code>web.xml</code>, <code>web-fragment.xml</code>,
        <code>tomcat-web.xml</code>, <code>*.tld</code>, <code>*.jspx</code>,
        <code>*.tagx</code> and <code>tagPlugins.xml</code> files for this web
        application will not permit external entities to be loaded. If not
        specified, the default value of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_xmlNamespaceAware"><td><code class="attributeName">xmlNamespaceAware</code></td><td>
        <p>If the value of this flag is <code>true</code>, the parsing of
        <code>web.xml</code>, <code>web-fragment.xml</code> and
        <code>tomcat-web.xml</code> files for this web application will be
        namespace-aware. Note that <code>*.tld</code>, <code>*.jspx</code> and
        <code>*.tagx</code> files are always parsed using a namespace-aware
        parser and that the <code>tagPlugins.xml</code> file (if any) is never
        parsed using a namespace-aware parser. Note also that if you turn this
        flag on, you should probably also turn <code>xmlValidation</code> on. If
        the <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code>
        <a href="systemprops.html">system property</a> is set to
        <code>true</code>, the default value of this attribute will be
        <code>true</code>, else the default value will be <code>false</code>.
        Setting this attribute to <code>true</code> will incur a performance
        penalty.</p>
      </td></tr><tr id="Attributes_Common Attributes_xmlValidation"><td><code class="attributeName">xmlValidation</code></td><td>
        <p>If the value of this flag is <code>true</code>, the parsing of
        <code>web.xml</code>, <code>web-fragment.xml</code> and
        <code>tomcat-web.xml</code> files for this web application will use a
        validating parser. If the
        <code>org.apache.catalina.STRICT_SERVLET_COMPLIANCE</code>
        <a href="systemprops.html">system property</a> is set to
        <code>true</code>, the default value of this attribute will be
        <code>true</code>, else the default value will be <code>false</code>.
        Setting this attribute to <code>true</code> will incur a performance
        penalty.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Standard_Implementation">Standard Implementation</h4><div class="text">

    <p>The standard implementation of <strong>Context</strong> is
    <strong>org.apache.catalina.core.StandardContext</strong>.
    It supports the following additional attributes (in addition to the
    common attributes listed above):</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementation_addWebinfClassesResources"><td><code class="attributeName">addWebinfClassesResources</code></td><td>
        <p>This attribute controls if, in addition to static resources being
        served from <code>META-INF/resources</code> inside web application JAR
        files, static resources are also served from
        <code>WEB-INF/classes/META-INF/resources</code>. This only applies to
        web applications with a major version of 3 or higher. Since this is a
        proprietary extension to the Servlet 3 specification, it is disabled by
        default. To enable this feature, set the attribute to <code>true</code>.
        </p>
      </td></tr><tr id="Attributes_Standard Implementation_antiResourceLocking"><td><code class="attributeName">antiResourceLocking</code></td><td>
        <p>If <code>true</code>, Tomcat will prevent any file locking.
        This will significantly impact startup time of applications,
        but allows full webapp hot deploy and undeploy on platforms
        or configurations where file locking can occur.
        If not specified, the default value is <code>false</code>.</p>

        <p>Please note that setting this to <code>true</code> has some side
        effects, including the disabling of JSP reloading in a running server:
        see <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=37668">
        Bugzilla 37668</a>.</p>

        <p>Please note that setting this flag to <code>true</code> in
        applications that are outside the <code>appBase</code> for the Host (the
        <code>webapps</code> directory by default) will cause the application to
        be <strong>deleted</strong> on Tomcat shutdown.  You probably don't want
        to do this, so think twice before setting antiResourceLocking=true on a
        webapp that's outside the <code>appBase</code> for its Host.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesHttpClientKeepAliveThread"><td><code class="attributeName">clearReferencesHttpClientKeepAliveThread</code></td><td>
        <p>If <code>true</code> and an <code>sun.net.www.http.HttpClient</code>
        keep-alive timer thread has been started by this web application and is
        still running, Tomcat will change the context class loader for that
        thread from the web application class loader to the parent of the web
        application class loader to prevent a memory leak. Note that the
        keep-alive timer thread will stop on its own once the keep-alives all
        expire however, on a busy system that might not happen for some time. If
        not specified, the default value of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesObjectStreamClassCaches"><td><code class="attributeName">clearReferencesObjectStreamClassCaches</code></td><td>
        <p>If <code>true</code>, when the web application is stopped Tomcat
        looks for <code>SoftReference</code>s to classes loaded by the web
        application in the <code>ObjectStreamClass</code> class used for
        serialization and clears any <code>SoftReference</code>s it finds. This
        feature uses reflection to identify the <code>SoftReference</code>s and
        therefore requires that the command line option
        <code>-XaddExports:java.base/java.io=ALL-UNNAMED</code> is set
        when running on Java 9 and above. If not specified, the default value of
        <code>true</code> will be used.</p>
        <p>The memory leak associated with <code>ObjectStreamClass</code> has
        been fixed in Java 19 onwards, Java 17.0.4 onwards and Java 11.0.16
        onwards. The check will be disabled when running on a version
        of Java that contains the fix.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesRmiTargets"><td><code class="attributeName">clearReferencesRmiTargets</code></td><td>
        <p>If <code>true</code>, Tomcat looks for memory leaks associated with
        RMI Targets and clears any it finds. This feature uses reflection to
        identify the leaks and therefore requires that the command line option
        <code>-XaddExports:java.rmi/sun.rmi.transport=ALL-UNNAMED</code> is set
        when running on Java 9 and above. Applications without memory leaks
        should operate correctly with this attribute set to <code>false</code>.
        If not specified, the default value of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesStopThreads"><td><code class="attributeName">clearReferencesStopThreads</code></td><td>
        <p>If <code>true</code>, Tomcat attempts to terminate threads that have
        been started by the web application. Stopping threads is performed via
        the deprecated (for good reason) <code>Thread.stop()</code> method and
        is likely to result in instability. As such, enabling this should be
        viewed as an option of last resort in a development environment and is
        not recommended in a production environment. If not specified, the
        default value of <code>false</code> will be used. If this feature is
        enabled, web applications may take up to two seconds longer to stop as
        executor threads are given up to two seconds to stop gracefully before
        <code>Thread.stop()</code> is called on any remaining threads.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesStopTimerThreads"><td><code class="attributeName">clearReferencesStopTimerThreads</code></td><td>
        <p>If <code>true</code>, Tomcat attempts to terminate
        <code>java.util.Timer</code> threads that have been started by the web
        application. Unlike standard threads, timer threads can be stopped
        safely although there may still be side-effects for the application. If
        not specified, the default value of <code>false</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_clearReferencesThreadLocals"><td><code class="attributeName">clearReferencesThreadLocals</code></td><td>
        <p>If <code>true</code>, Tomcat attempts to clear
        <code>java.lang.ThreadLocal</code> variables that have been populated
        with classes loaded by the web application. If not specified, the
        default value of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_copyXML"><td><code class="attributeName">copyXML</code></td><td>
        <p>Set to <code>true</code> if you want a context XML descriptor
        embedded inside the application (located at
        <code>/META-INF/context.xml</code>) to be copied to the owning
        <a href="host.html">Host</a>'s <code>xmlBase</code> when the application
        is deployed. On subsequent starts, the copied context XML descriptor
        will be used in preference to any context XML descriptor embedded inside
        the application even if the descriptor embedded inside the application
        is more recent. The default is <code>false</code>. Note if
        the <strong>deployXML</strong> attribute of the owning
        <a href="host.html">Host</a> is <code>false</code> or if the
        <strong>copyXML</strong> attribute of the owning
        <a href="host.html">Host</a> is <code>true</code>, this attribute will
        have no effect.</p>
      </td></tr><tr id="Attributes_Standard Implementation_jndiExceptionOnFailedWrite"><td><code class="attributeName">jndiExceptionOnFailedWrite</code></td><td>
        <p>If <code>true</code>, any attempt by an application to modify the
        provided JNDI context with a call to bind(), unbind(),
        createSubContext(), destroySubContext() or close() will trigger a
        <code>javax.naming.OperationNotSupportedException</code> as required by
        section EE.5.3.4 of the Java EE specification. This exception can be
        disabled by setting this attribute to <code>false</code> in which case
        any calls to modify the JNDI context will return <b>without</b> making
        any changes and methods that return values will return
        <code>null</code>. If not specified, the specification compliant default
        of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_notFoundClassResourceCacheSize"><td><code class="attributeName">notFoundClassResourceCacheSize</code></td><td>
        <p>Class resources are not cached by the standard resources
        implementation since they are loaded on first use and then the resource
        is then no longer required. It does help, however, to cache classes that
        are not found as in some scenarios the same class will be searched for
        many times and the greater the number of JARs/classes, the longer that
        search will take. An LRU cache is used and this attribute controls the
        size of that cache. A value of less than 1 disables the cache. If not
        specified, the default value of 1000 will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_renewThreadsWhenStoppingContext"><td><code class="attributeName">renewThreadsWhenStoppingContext</code></td><td>
        <p>If <code>true</code>, when this context is stopped, Tomcat renews all
        the threads from the thread pool that was used to serve this context.
        This also requires that the
        <code>ThreadLocalLeakPreventionListener</code> be configured in
        <code>server.xml</code> and that the <code>threadRenewalDelay</code>
        property of the <code>Executor</code> be &gt;=0. If not specified, the
        default value of <code>true</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_skipMemoryLeakChecksOnJvmShutdown"><td><code class="attributeName">skipMemoryLeakChecksOnJvmShutdown</code></td><td>
        <p>If <code>true</code>, Tomcat will not perform the usual memory leak
        checks when the web application is stopped if that web application is
        stopped as part of a JVM shutdown. If not specified, the default value
        of <code>false</code> will be used.</p>
      </td></tr><tr id="Attributes_Standard Implementation_unloadDelay"><td><code class="attributeName">unloadDelay</code></td><td>
        <p>Number of ms that the container will wait for servlets to unload.
        If not specified, the default value is <code>2000</code> ms.</p>
      </td></tr><tr id="Attributes_Standard Implementation_unpackWAR"><td><code class="attributeName">unpackWAR</code></td><td>
        <p>If <code>false</code>, the <strong>unpackWARs</strong> attribute of
        the owning <a href="host.html">Host</a> will be overridden and the WAR
        file will not be unpacked. If <code>true</code>, the value of the owning
        <a href="host.html">Host</a>'s <strong>unpackWARs</strong>
        attribute will determine if the WAR is unpacked. If not specified, the
        default value is <code>true</code>.</p>
      </td></tr><tr id="Attributes_Standard Implementation_useNaming"><td><code class="attributeName">useNaming</code></td><td>
        <p>Set to <code>true</code> (the default) to have Catalina enable a
        JNDI <code>InitialContext</code> for this web application that is
        compatible with Java2 Enterprise Edition (J2EE) platform
        conventions.</p>
      </td></tr><tr id="Attributes_Standard Implementation_workDir"><td><code class="attributeName">workDir</code></td><td>
        <p>Pathname to a scratch directory to be provided by this Context
        for temporary read-write use by servlets within the associated web
        application.  This directory will be made visible to servlets in the
        web application by a servlet context attribute (of type
        <code>java.io.File</code>) named
        <code>javax.servlet.context.tempdir</code> as described in the
        Servlet Specification.  If not specified, a suitable directory
        underneath <code>$CATALINA_BASE/work</code> will be provided.</p>
      </td></tr></table>

  </div></div>


</div><h3 id="Nested_Components">Nested Components</h3><div class="text">

  <p>You can nest at most one instance of the following utility components
  by nesting a corresponding element inside your <strong>Context</strong>
  element:</p>
  <ul>
  <li><a href="cookie-processor.html"><strong>Cookie Processor</strong></a> -
      Configure parsing and generation of HTTP cookie headers.</li>
  <li><a href="loader.html"><strong>Loader</strong></a> -
      Configure the web application class loader that will be used to load
      servlet and bean classes for this web application.  Normally, the
      default configuration of the class loader will be sufficient.</li>
  <li><a href="manager.html"><strong>Manager</strong></a> -
      Configure the session manager that will be used to create, destroy,
      and persist HTTP sessions for this web application.  Normally, the
      default configuration of the session manager will be sufficient.</li>
  <li><a href="realm.html"><strong>Realm</strong></a> -
      Configure a realm that will allow its
      database of users, and their associated roles, to be utilized solely
      for this particular web application.  If not specified, this web
      application will utilize the Realm associated with the owning
      <a href="host.html">Host</a> or <a href="engine.html">Engine</a>.</li>
  <li><a href="resources.html"><strong>Resources</strong></a> -
      Configure the resource manager that will be used to access the static
      resources associated with this web application.  Normally, the
      default configuration of the resource manager will be sufficient.</li>
  <li><strong>WatchedResource</strong> - The auto deployer will monitor the
      specified static resource of the web application for updates, and will
      reload the web application if it is updated. The content of this element
      must be a string.</li>
  <li><a href="jar-scanner.html"><strong>JarScanner</strong></a> -
      Configure the Jar Scanner that will be used to scan the web application
      for JAR files and directories of class files. It is typically used during
      web application start to identify configuration files such as TLDs o
      web-fragment.xml files that must be processed as part of the web
      application initialisation.  Normally, the default Jar Scanner
      configuration will be sufficient.</li>
  </ul>

</div><h3 id="Special_Features">Special Features</h3><div class="text">


  <div class="subsection"><h4 id="Logging">Logging</h4><div class="text">

    <p>A context is associated with the
       <code>org.apache.catalina.core.ContainerBase.[enginename].[hostname].[path]</code>
       log category.  Note that the brackets are actually part of the name, don't omit them.</p>

  </div></div>


  <div class="subsection"><h4 id="Access_Logs">Access Logs</h4><div class="text">

    <p>When you run a web server, one of the output files normally generated
    is an <em>access log</em>, which generates one line of information for
    each request processed by the server, in a standard format.  Catalina
    includes an optional <a href="valve.html">Valve</a> implementation that
    can create access logs in the same standard format created by web servers,
    or in any number of custom formats.</p>

    <p>You can ask Catalina to create an access log for all requests
    processed by an <a href="engine.html">Engine</a>,
    <a href="host.html">Host</a>, or <a href="context.html">Context</a>
    by nesting a <a href="valve.html">Valve</a> element like this:</p>

<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Valve className="org.apache.catalina.valves.AccessLogValve"
         prefix="localhost_access_log" suffix=".txt"
         pattern="common"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>See <a href="valve.html#Access_Logging">Access Logging Valves</a>
    for more information on the configuration attributes that are
    supported.</p>

  </div></div>


  <div class="subsection"><h4 id="Automatic_Context_Configuration">Automatic Context Configuration</h4><div class="text">

    <p>If you use the standard <strong>Context</strong> implementation,
    the following configuration steps occur automatically when Catalina
    is started, or whenever this web application is reloaded.  No special
    configuration is required to enable this feature.</p>

    <ul>
    <li>If you have not declared your own <a href="loader.html">Loader</a>
       element, a standard web application class loader will be configured.
       </li>
    <li>If you have not declared your own <a href="manager.html">Manager</a>
        element, a standard session manager will be configured.</li>
    <li>If you have not declared your own <a href="resources.html">Resources</a>
        element, a standard resources manager will be configured.</li>
    <li>The web application properties listed in <code>conf/web.xml</code>
        will be processed as defaults for this web application.  This is used
        to establish default mappings (such as mapping the <code>*.jsp</code>
        extension to the corresponding JSP servlet), and other standard
        features that apply to all web applications.</li>
    <li>The web application properties listed in the
        <code>/WEB-INF/tomcat-web.xml</code> resource for this web application
        will be processed (if this resource exists), taking precedence over the
        defaults.</li>
    <li>The web application properties listed in the
        <code>/WEB-INF/web.xml</code> resource for this web application
        will be processed (if this resource exists).</li>
    <li>If your web application has specified security constraints that might
        require user authentication, an appropriate Authenticator that
        implements the login method you have selected will be configured.</li>
    </ul>

  </div></div>


  <div class="subsection"><h4 id="Context_Parameters">Context Parameters</h4><div class="text">

    <p>You can configure named values that will be made visible to the
    web application as servlet context initialization parameters by nesting
    <code>&lt;Parameter&gt;</code> elements inside this element.  For
    example, you can create an initialization parameter like this:</p>
<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Parameter name="companyName" value="My Company, Incorporated"
         override="false"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>This is equivalent to the inclusion of the following element in the
    web application deployment descriptor (<code>/WEB-INF/web.xml</code>):
    </p>
<div class="codeBox"><pre><code>&lt;context-param&gt;
  &lt;param-name&gt;companyName&lt;/param-name&gt;
  &lt;param-value&gt;My Company, Incorporated&lt;/param-value&gt;
&lt;/context-param&gt;</code></pre></div>
    <p>but does <em>not</em> require modification of the deployment descriptor
    to customize this value.</p>

    <p>The valid attributes for a <code>&lt;Parameter&gt;</code> element
    are as follows:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Context Parameters_description"><td><code class="attributeName">description</code></td><td>
        <p>Optional, human-readable description of this context
        initialization parameter.</p>
      </td></tr><tr id="Special Features_Context Parameters_name"><td><strong><code class="attributeName">name</code></strong></td><td>
        <p>The name of the context initialization parameter to be created.</p>
      </td></tr><tr id="Special Features_Context Parameters_override"><td><code class="attributeName">override</code></td><td>
        <p>Set this to <code>false</code> if you do <strong>not</strong> want
        a <code>&lt;context-param&gt;</code> for the same parameter name,
        found in the web application deployment descriptor, to override the
        value specified here.  By default, overrides are allowed.</p>
      </td></tr><tr id="Special Features_Context Parameters_value"><td><strong><code class="attributeName">value</code></strong></td><td>
        <p>The parameter value that will be presented to the application
        when requested by calling
        <code>ServletContext.getInitParameter()</code>.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Environment_Entries">Environment Entries</h4><div class="text">

    <p>You can configure named values that will be made visible to the
    web application as environment entry resources, by nesting
    <code>&lt;Environment&gt;</code> entries inside this element.  For
    example, you can create an environment entry like this:</p>
<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Environment name="maxExemptions" value="10"
         type="java.lang.Integer" override="false"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>This is equivalent to the inclusion of the following element in the
    web application deployment descriptor (<code>/WEB-INF/web.xml</code>):
    </p>
<div class="codeBox"><pre><code>&lt;env-entry&gt;
  &lt;env-entry-name&gt;maxExemptions&lt;/env-entry-name&gt;
  &lt;env-entry-value&gt;10&lt;/env-entry-value&gt;
  &lt;env-entry-type&gt;java.lang.Integer&lt;/env-entry-type&gt;
&lt;/env-entry&gt;</code></pre></div>
    <p>but does <em>not</em> require modification of the deployment descriptor
    to customize this value.</p>

    <p>The valid attributes for an <code>&lt;Environment&gt;</code> element
    are as follows:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Environment Entries_description"><td><code class="attributeName">description</code></td><td>
        <p>Optional, human-readable description of this environment entry.</p>
      </td></tr><tr id="Special Features_Environment Entries_name"><td><strong><code class="attributeName">name</code></strong></td><td>
        <p>The name of the environment entry to be created, relative to the
        <code>java:comp/env</code> context.</p>
      </td></tr><tr id="Special Features_Environment Entries_override"><td><code class="attributeName">override</code></td><td>
        <p>Set this to <code>false</code> if you do <strong>not</strong> want
        an <code>&lt;env-entry&gt;</code> for the same environment entry name,
        found in the web application deployment descriptor, to override the
        value specified here.  By default, overrides are allowed.</p>
      </td></tr><tr id="Special Features_Environment Entries_type"><td><strong><code class="attributeName">type</code></strong></td><td>
        <p>The fully qualified Java class name expected by the web application
        for this environment entry.  Must be a legal value for
        <code>&lt;env-entry-type&gt;</code> in the web application deployment
        descriptor.</p>
      </td></tr><tr id="Special Features_Environment Entries_value"><td><strong><code class="attributeName">value</code></strong></td><td>
        <p>The parameter value that will be presented to the application
        when requested from the JNDI context.  This value must be convertible
        to the Java type defined by the <code>type</code> attribute.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Lifecycle_Listeners">Lifecycle Listeners</h4><div class="text">

    <p>If you have implemented a Java object that needs to know when this
    <strong>Context</strong> is started or stopped, you can declare it by
    nesting a <strong>Listener</strong> element inside this element.  The
    class name you specify must implement the
    <code>org.apache.catalina.LifecycleListener</code> interface, and
    the class must be packaged in a jar and placed in the
    <code>$CATALINA_HOME/lib</code> directory.
    It will be notified about the occurrence of the corresponding
    lifecycle events.  Configuration of such a listener looks like this:</p>

<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Listener className="com.mycompany.mypackage.MyListener" ... &gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>Note that a Listener can have any number of additional properties
    that may be configured from this element.  Attribute names are matched
    to corresponding JavaBean property names using the standard property
    method naming patterns.</p>

  </div></div>


  <div class="subsection"><h4 id="Request_Filters">Request Filters</h4><div class="text">

    <p>You can ask Catalina to check the IP address, or host name, on every
    incoming request directed to the surrounding
    <a href="engine.html">Engine</a>, <a href="host.html">Host</a>, or
    <a href="context.html">Context</a> element.  The remote address or name
    will be checked against configured "accept" and/or "deny"
    filters, which are defined using <code>java.util.regex</code> Regular
    Expression syntax.  Requests that come from locations that are
    not accepted will be rejected with an HTTP "Forbidden" error.
    Example filter declarations:</p>

<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Valve className="org.apache.catalina.valves.RemoteHostValve"
         allow=".*\.mycompany\.com|www\.yourcompany\.com"/&gt;
  &lt;Valve className="org.apache.catalina.valves.RemoteAddrValve"
         deny="192\.168\.1\.\d+"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>See <a href="valve.html#Remote_Address_Filter">Remote Address Filter</a>
    and <a href="valve.html#Remote_Host_Filter">Remote Host Filter</a> for
    more information about the configuration options that are supported.</p>

  </div></div>


  <div class="subsection"><h4 id="Resource_Definitions">Resource Definitions</h4><div class="text">

    <p>You can declare the characteristics of the resource
    to be returned for JNDI lookups of <code>&lt;resource-ref&gt;</code> and
    <code>&lt;resource-env-ref&gt;</code> elements in the web application
    deployment descriptor.  You <strong>MUST</strong> also define
    the needed resource parameters as attributes of the <code>Resource</code>
    element, to configure the object factory to be used (if not known to Tomcat
    already), and the properties used to configure that object factory.</p>

    <p>For example, you can create a resource definition like this:</p>
<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;Resource name="jdbc/EmployeeDB" auth="Container"
            type="javax.sql.DataSource"
     description="Employees Database for HR Applications"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

    <p>This is equivalent to the inclusion of the following element in the
    web application deployment descriptor (<code>/WEB-INF/web.xml</code>):</p>
<div class="codeBox"><pre><code>&lt;resource-ref&gt;
  &lt;description&gt;Employees Database for HR Applications&lt;/description&gt;
  &lt;res-ref-name&gt;jdbc/EmployeeDB&lt;/res-ref-name&gt;
  &lt;res-ref-type&gt;javax.sql.DataSource&lt;/res-ref-type&gt;
  &lt;res-auth&gt;Container&lt;/res-auth&gt;
&lt;/resource-ref&gt;</code></pre></div>

    <p>but does <em>not</em> require modification of the deployment
    descriptor to customize this value.</p>

    <p>The valid attributes for a <code>&lt;Resource&gt;</code> element
    are as follows:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Resource Definitions_auth"><td><code class="attributeName">auth</code></td><td>
        <p>Specify whether the web Application code signs on to the
        corresponding resource manager programmatically, or whether the
        Container will sign on to the resource manager on behalf of the
        application.  The value of this attribute must be
        <code>Application</code> or <code>Container</code>.  This
        attribute is <strong>required</strong> if the web application
        will use a <code>&lt;resource-ref&gt;</code> element in the web
        application deployment descriptor, but is optional if the
        application uses a <code>&lt;resource-env-ref&gt;</code> instead.</p>
      </td></tr><tr id="Special Features_Resource Definitions_closeMethod"><td><code class="attributeName">closeMethod</code></td><td>
        <p>Name of the zero-argument method to call on a singleton resource when
        it is no longer required. This is intended to speed up clean-up of
        resources that would otherwise happen as part of garbage collection.
        This attribute is ignored if the <code>singleton</code> attribute is
        <code>false</code>.</p>
        <p>For <code>javax.sql.DataSource</code> and
        <code>javax.sql.XADataSource</code> resources that implement
        <code>AutoCloseable</code> such as Apache Commons DBCP 2 and the default
        Apache Tomcat connection pool, this attribute is defaults to
        <code>close</code>. This may be disabled by setting the attribute to the
        empty string. For all other resource types no default is defined and no
        close method will be called by default.</p>
      </td></tr><tr id="Special Features_Resource Definitions_description"><td><code class="attributeName">description</code></td><td>
        <p>Optional, human-readable description of this resource.</p>
      </td></tr><tr id="Special Features_Resource Definitions_name"><td><strong><code class="attributeName">name</code></strong></td><td>
        <p>The name of the resource to be created, relative to the
        <code>java:comp/env</code> context.</p>
      </td></tr><tr id="Special Features_Resource Definitions_scope"><td><code class="attributeName">scope</code></td><td>
        <p>Specify whether connections obtained through this resource
        manager can be shared.  The value of this attribute must be
        <code>Shareable</code> or <code>Unshareable</code>.  By default,
        connections are assumed to be shareable.</p>
      </td></tr><tr id="Special Features_Resource Definitions_singleton"><td><code class="attributeName">singleton</code></td><td>
        <p>Specify whether this resource definition is for a singleton resource,
        i.e. one where there is only a single instance of the resource. If this
        attribute is <code>true</code>, multiple JNDI lookups for this resource
        will return the same object. If this attribute is <code>false</code>,
        multiple JNDI lookups for this resource will return different objects.
        This attribute must be <code>true</code> for
        <code>javax.sql.DataSource</code> resources to enable JMX registration
        of the DataSource. The value of this attribute must be <code>true</code>
        or <code>false</code>. By default, this attribute is <code>true</code>.
        </p>
      </td></tr><tr id="Special Features_Resource Definitions_type"><td><strong><code class="attributeName">type</code></strong></td><td>
        <p>The fully qualified Java class name expected by the web
        application when it performs a lookup for this resource.</p>
      </td></tr></table>


  </div></div>


  <div class="subsection"><h4 id="Resource_Links">Resource Links</h4><div class="text">

     <p>This element is used to create a link to a global JNDI resource. Doing
     a JNDI lookup on the link name will then return the linked global
     resource.</p>

    <p>For example, you can create a resource link like this:</p>
<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;ResourceLink name="linkToGlobalResource"
            global="simpleValue"
            type="java.lang.Integer"
  ...
&lt;/Context&gt;</code></pre></div>

    <p>The valid attributes for a <code>&lt;ResourceLink&gt;</code> element
    are as follows:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Resource Links_global"><td><strong><code class="attributeName">global</code></strong></td><td>
        <p>The name of the linked global resource in the
        global JNDI context.</p>
      </td></tr><tr id="Special Features_Resource Links_name"><td><strong><code class="attributeName">name</code></strong></td><td>
        <p>The name of the resource link to be created, relative to the
        <code>java:comp/env</code> context.</p>
      </td></tr><tr id="Special Features_Resource Links_type"><td><strong><code class="attributeName">type</code></strong></td><td>
        <p>The fully qualified Java class name expected by the web
        application when it performs a lookup for this resource link.</p>
      </td></tr><tr id="Special Features_Resource Links_factory"><td><code class="attributeName">factory</code></td><td>
        <p>The fully qualified Java class name for the class creating these objects.
        This class should implement the <code>javax.naming.spi.ObjectFactory</code> interface.</p>
      </td></tr></table>

    <p>When the attribute <code>factory="org.apache.naming.factory.DataSourceLinkFactory"</code> the resource link can be used with
       two additional attributes to allow a shared data source to be used with different credentials.
       When these two additional attributes are used in combination with the <code>javax.sql.DataSource</code>
       type, different contexts can share a global data source with different credentials.
       Under the hood, what happens is that a call to <a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/DataSource.html#getConnection()"><code>getConnection()</code></a>
       is simply translated to a call <a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/DataSource.html#getConnection(java.lang.String,%20java.lang.String)">
       <code>getConnection(username, password)</code></a> on the global data source. This is an easy way to get code to be transparent to what schemas are being used,
       yet be able to control connections (or pools) in the global configuration.
    </p>
    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Resource Links_username"><td><code class="attributeName">username</code></td><td>
        <p><code>username</code> value for the <code>getConnection(username, password)</code>
           call on the linked global DataSource.
        </p>
      </td></tr><tr id="Special Features_Resource Links_password"><td><code class="attributeName">password</code></td><td>
        <p><code>password</code> value for the <code>getConnection(username, password)</code>
           call on the linked global DataSource.
        </p>
      </td></tr></table>
    <p>Shared Data Source Example:</p>
    <p><strong>Warning:</strong> This feature works only if the global DataSource
supports <code>getConnection(username, password)</code> method.
<a href="https://commons.apache.org/dbcp/">Apache Commons DBCP 2</a> pool that
Tomcat uses by default does not support it. See its Javadoc for
<code>BasicDataSource</code> class.
<a href="../jdbc-pool.html">Apache Tomcat JDBC pool</a> does support it,
but by default this support is disabled and can be enabled by
<code>alternateUsernameAllowed</code> attribute. See its documentation
for details.</p>
<div class="codeBox"><pre><code>&lt;GlobalNamingResources&gt;
  ...
  &lt;Resource name="sharedDataSource"
            global="sharedDataSource"
            type="javax.sql.DataSource"
            factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
            alternateUsernameAllowed="true"
            username="bar"
            password="barpass"
            ...
  ...
&lt;/GlobalNamingResources&gt;

&lt;Context path="/foo"...&gt;
  ...
  &lt;ResourceLink
            name="appDataSource"
            global="sharedDataSource"
            type="javax.sql.DataSource"
            factory="org.apache.naming.factory.DataSourceLinkFactory"
            username="foo"
            password="foopass"
  ...
&lt;/Context&gt;
&lt;Context path="/bar"...&gt;
  ...
  &lt;ResourceLink
            name="appDataSource"
            global="sharedDataSource"
            type="javax.sql.DataSource"
  ...
&lt;/Context&gt;</code></pre></div>
    <p>When a request for <code>getConnection()</code> is made in the
       <code>/foo</code> context, the request is translated into
       <code>getConnection("foo","foopass")</code>,
       while a request in the <code>/bar</code> gets passed straight through.</p>
  </div></div>

  <div class="subsection"><h4 id="Transaction">Transaction</h4><div class="text">

    <p>You can declare the characteristics of the UserTransaction
    to be returned for JNDI lookup for <code>java:comp/UserTransaction</code>.
    You <strong>MUST</strong> define an object factory class to instantiate
    this object as well as the needed resource parameters as attributes of the
    <code>Transaction</code>
    element, and the properties used to configure that object factory.</p>

    <p>The valid attributes for the <code>&lt;Transaction&gt;</code> element
    are as follows:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Special Features_Transaction_factory"><td><strong><code class="attributeName">factory</code></strong></td><td>
        <p>The class name for the JNDI object factory.</p>
      </td></tr></table>

  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>