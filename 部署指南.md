# 学校党员信息管理系统 - 部署指南

## 系统要求

### 软件环境
- **JDK**: 1.8 或更高版本
- **Web服务器**: Apache Tomcat 9.0+ 或 Jetty 9.4+
- **数据库**: MySQL 8.0+ 或 MariaDB 10.3+
- **浏览器**: Chrome 70+, Firefox 65+, Safari 12+, Edge 79+

### 硬件要求
- **CPU**: 双核 2.0GHz 或更高
- **内存**: 4GB RAM 或更高
- **存储**: 至少 2GB 可用空间
- **网络**: 100Mbps 或更高（如需远程访问）

## 部署步骤

### 1. 环境准备

#### 1.1 安装JDK
```bash
# 检查Java版本
java -version

# 如果未安装，请下载并安装JDK 8+
# 设置JAVA_HOME环境变量
export JAVA_HOME=/path/to/jdk
export PATH=$JAVA_HOME/bin:$PATH
```

#### 1.2 安装MySQL数据库
```bash
# 安装MySQL（以Ubuntu为例）
sudo apt update
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 1.3 安装Tomcat
```bash
# 下载Tomcat 9.0
wget https://downloads.apache.org/tomcat/tomcat-9/v9.0.x/bin/apache-tomcat-9.0.x.tar.gz

# 解压
tar -xzf apache-tomcat-9.0.x.tar.gz
sudo mv apache-tomcat-9.0.x /opt/tomcat

# 设置权限
sudo chown -R tomcat:tomcat /opt/tomcat
sudo chmod +x /opt/tomcat/bin/*.sh
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS management
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE management;
```

#### 2.2 执行数据库脚本
```bash
# 执行建表脚本
mysql -u root -p management < src/main/resources/database.sql
```

#### 2.3 数据库连接配置
当前配置 `src/main/resources/db.properties` 文件：
```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=************************************************************************************************************************
db.username=root
db.password=123456
db.initialSize=5
db.maxActive=20
db.maxIdle=10
db.minIdle=5
db.maxWait=60000
```

**注意**: 当前使用root用户连接，生产环境建议创建专用数据库用户。

### 3. 项目编译和打包

#### 3.1 下载依赖jar包
需要下载以下jar包并放入项目的lib目录：
- `mysql-connector-java-8.0.33.jar`
- `servlet-api.jar`（通常由Tomcat提供）

#### 3.2 编译项目
```bash
# 创建编译目录
mkdir -p build/classes

# 编译Java源文件
javac -cp "lib/*:$TOMCAT_HOME/lib/*" -d build/classes src/main/java/com/school/management/**/*.java

# 复制资源文件
cp -r src/main/resources/* build/classes/
```

#### 3.3 打包WAR文件
```bash
# 创建WAR包目录结构
mkdir -p build/war/WEB-INF/classes
mkdir -p build/war/WEB-INF/lib

# 复制编译后的类文件
cp -r build/classes/* build/war/WEB-INF/classes/

# 复制依赖jar包
cp lib/*.jar build/war/WEB-INF/lib/

# 复制Web资源
cp -r src/main/webapp/* build/war/

# 创建WAR文件
cd build/war
jar -cvf ../school-party-management.war *
cd ../..
```

### 4. 部署到Tomcat

#### 4.1 部署WAR文件
```bash
# 复制WAR文件到Tomcat webapps目录
cp build/school-party-management.war $TOMCAT_HOME/webapps/

# 或者解压到指定目录
mkdir $TOMCAT_HOME/webapps/school-party-management
cd $TOMCAT_HOME/webapps/school-party-management
jar -xvf ../school-party-management.war
```

#### 4.2 配置Tomcat
编辑 `$TOMCAT_HOME/conf/server.xml`，确保端口配置正确：
```xml
<Connector port="8080" protocol="HTTP/1.1"
           connectionTimeout="20000"
           redirectPort="8443" 
           URIEncoding="UTF-8"/>
```

#### 4.3 启动Tomcat
```bash
# 启动Tomcat
$TOMCAT_HOME/bin/startup.sh

# 查看日志
tail -f $TOMCAT_HOME/logs/catalina.out
```

### 5. 验证部署

#### 5.1 访问系统
打开浏览器访问：
- 主页面: `http://localhost:8080/school-party-management/index.html`
- 测试页面: `http://localhost:8080/school-party-management/test.html`
- API接口: `http://localhost:8080/school-party-management/api/applicant`

#### 5.2 功能测试
1. **数据库连接测试**
   - 访问测试页面
   - 点击"测试数据库连接"按钮

2. **前端功能测试**
   - 测试主页面加载
   - 测试导航菜单
   - 测试入党申请人模块

3. **API接口测试**
   ```bash
   # 测试获取申请人列表
   curl -X GET http://localhost:8080/school-party-management/api/applicant
   
   # 测试添加申请人
   curl -X POST http://localhost:8080/school-party-management/api/applicant \
        -d "name=测试用户&gender=男&studentId=TEST001&department=计算机学院"
   ```

## 常见问题解决

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库配置信息
- 确认MySQL JDBC驱动已正确添加

### 2. 中文乱码问题
- 确保数据库字符集为utf8mb4
- 检查Tomcat URIEncoding配置
- 验证页面meta标签设置

### 3. 404错误
- 检查WAR文件是否正确部署
- 验证Tomcat启动日志
- 确认访问路径正确

### 4. 500内部服务器错误
- 查看Tomcat错误日志
- 检查Java类路径配置
- 验证数据库连接

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 定期清理日志表
- 配置数据库连接池

### 2. 应用优化
- 启用Gzip压缩
- 配置静态资源缓存
- 优化SQL查询

### 3. 服务器优化
- 调整JVM内存参数
- 配置Tomcat连接池
- 启用HTTP/2

## 安全配置

### 1. 数据库安全
- 使用强密码
- 限制数据库用户权限
- 定期备份数据

### 2. 应用安全
- 配置HTTPS
- 添加访问控制
- 输入数据验证

### 3. 服务器安全
- 关闭不必要的端口
- 配置防火墙
- 定期更新系统

## 监控和维护

### 1. 日志监控
- 定期检查应用日志
- 监控数据库性能
- 设置错误告警

### 2. 备份策略
- 定期备份数据库
- 备份应用配置文件
- 制定恢复计划

### 3. 更新维护
- 定期更新依赖库
- 应用安全补丁
- 性能调优

---

**注意**: 本指南适用于开发和测试环境。生产环境部署需要额外的安全配置和性能优化。
