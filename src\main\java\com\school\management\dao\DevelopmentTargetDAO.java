package com.school.management.dao;

import com.school.management.entity.DevelopmentTarget;
import java.util.List;

/**
 * 发展对象DAO接口
 * 继承基础DAO接口，并添加特定的查询方法
 */
public interface DevelopmentTargetDAO extends BaseDAO<DevelopmentTarget> {
    
    /**
     * 根据学号查询发展对象
     * @param studentId 学号
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget selectByStudentId(String studentId);
    
    /**
     * 根据身份证号查询发展对象
     * @param idCard 身份证号
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget selectByIdCard(String idCard);
    
    /**
     * 根据积极分子ID查询发展对象
     * @param activistId 积极分子ID
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget selectByActivistId(Integer activistId);
    
    /**
     * 根据姓名模糊查询发展对象
     * @param name 姓名关键字
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByNameLike(String name);
    
    /**
     * 根据院系查询发展对象
     * @param department 院系名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByDepartment(String department);
    
    /**
     * 根据专业查询发展对象
     * @param major 专业名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByMajor(String major);
    
    /**
     * 根据班级查询发展对象
     * @param className 班级名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByClass(String className);
    
    /**
     * 根据状态查询发展对象
     * @param status 状态
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByStatus(String status);
    
    /**
     * 根据政治审查结果查询发展对象
     * @param result 政治审查结果
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByPoliticalReviewResult(String result);
    
    /**
     * 根据确定日期范围查询发展对象
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByDeterminationDateRange(String startDate, String endDate);
    
    /**
     * 根据培训成绩范围查询发展对象
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByTrainingScoreRange(Double minScore, Double maxScore);
    
    /**
     * 根据多个条件组合查询发展对象
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param politicalReviewResult 政治审查结果（可为null）
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByMultipleConditions(String name, String department, 
                                                     String major, String className, String status,
                                                     String politicalReviewResult);
    
    /**
     * 根据多个条件组合分页查询发展对象
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param politicalReviewResult 政治审查结果（可为null）
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 发展对象列表
     */
    List<DevelopmentTarget> selectByMultipleConditionsWithPage(String name, String department, 
                                                             String major, String className, String status,
                                                             String politicalReviewResult,
                                                             int offset, int limit);
    
    /**
     * 根据多个条件统计发展对象数量
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param politicalReviewResult 政治审查结果（可为null）
     * @return 发展对象数量
     */
    int countByMultipleConditions(String name, String department, 
                                 String major, String className, String status,
                                 String politicalReviewResult);
    
    /**
     * 获取所有院系列表
     * @return 院系名称列表
     */
    List<String> selectAllDepartments();
    
    /**
     * 获取指定院系的所有专业列表
     * @param department 院系名称
     * @return 专业名称列表
     */
    List<String> selectMajorsByDepartment(String department);
    
    /**
     * 获取指定专业的所有班级列表
     * @param major 专业名称
     * @return 班级名称列表
     */
    List<String> selectClassesByMajor(String major);
    
    /**
     * 统计各状态的发展对象数量
     * @return 状态统计Map，key为状态名称，value为数量
     */
    java.util.Map<String, Integer> countByStatus();
    
    /**
     * 统计各政治审查结果的发展对象数量
     * @return 政治审查结果统计Map
     */
    java.util.Map<String, Integer> countByPoliticalReviewResult();
    
    /**
     * 获取培训成绩统计信息
     * @return 包含平均分、最高分、最低分等统计信息的Map
     */
    java.util.Map<String, Object> getTrainingScoreStatistics();
}
