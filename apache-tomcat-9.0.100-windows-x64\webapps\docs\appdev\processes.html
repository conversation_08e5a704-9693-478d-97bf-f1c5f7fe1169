<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Application Developer's Guide (9.0.100) - Development Processes</title><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Application Developer's Guide</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">App Dev Guide Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Contents</h2><ul><li><a href="index.html">Contents</a></li><li><a href="introduction.html">Introduction</a></li><li><a href="installation.html">Installation</a></li><li><a href="deployment.html">Deployment</a></li><li><a href="source.html">Source Code</a></li><li><a href="processes.html">Processes</a></li><li><a href="sample/">Example App</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Development Processes</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Development_Processes">Development Processes</a><ol><li><a href="#One-Time_Setup_of_Ant_and_Tomcat_for_Development">One-Time Setup of Ant and Tomcat for Development</a></li><li><a href="#Create_Project_Source_Code_Directory">Create Project Source Code Directory</a></li><li><a href="#Edit_Source_Code_and_Pages">Edit Source Code and Pages</a></li><li><a href="#Build_the_Web_Application">Build the Web Application</a></li><li><a href="#Test_Your_Web_Application">Test Your Web Application</a></li><li><a href="#Creating_a_Release">Creating a Release</a></li></ol></li></ul>
</div><h3 id="Development_Processes">Development Processes</h3><div class="text">

<p>Although application development can take many forms, this manual proposes
a fairly generic process for creating web applications using Tomcat.  The
following sections highlight the commands and tasks that you, as the developer
of the code, will perform.  The same basic approach works when you have
multiple programmers involved, as long as you have an appropriate source code
control system and internal team rules about who is working on what parts
of the application at any given time.</p>

<p>The task descriptions below do not assume any particular source code control
system but simply identify when and what source code control tasks are typically
performed. You will need to idenitfy the appropriate source code control
commands for your system.</p>


<div class="subsection"><h4 id="One-Time_Setup_of_Ant_and_Tomcat_for_Development">One-Time Setup of Ant and Tomcat for Development</h4><div class="text">

<p>In order to take advantage of the special Ant tasks that interact with the
<em>Manager</em> web application, you need to perform the following tasks
once (no matter how many web applications you plan to develop).</p>
<ul>
<li><p><em>Configure the Ant custom tasks</em>.  The implementation code for the
    Ant custom tasks is in a JAR file named
    <code>$CATALINA_HOME/lib/catalina-ant.jar</code>, which must be
    copied in to the <code>lib</code> directory of your Ant installation.
    </p></li>
<li><p><em>Define one or more Tomcat users</em>.  The <em>Manager</em> web
    application runs under a security constraint that requires a user to be
    logged in, and have the security role <code>manager-script</code> assigned
    to then.  How such users are defined depends on which Realm you have
    configured in Tomcat's <code>conf/server.xml</code> file -- see the
    <a href="../realm-howto.html">Realm Configuration How-To</a> for more
    information.  You may define any number of users (with any username
    and password that you like) with the <code>manager-script</code> role.
    </p></li>
</ul>

</div></div>


<div class="subsection"><h4 id="Create_Project_Source_Code_Directory">Create Project Source Code Directory</h4><div class="text">

<p>The first step is to create a new project source directory, and customize
the <code>build.xml</code> and <code>build.properties</code> files you will
be using.  The directory structure is described in <a href="source.html">the
previous section</a>, or you can use the
<a href="sample/">sample application</a> as a starting point.</p>

<p>Create your project source directory, and define it within your source code
control system. This might be done by a series of commands like this:</p>
<div class="codeBox"><pre><code>cd {my home directory}
mkdir myapp &lt;-- Assumed "project source directory"
cd myapp
mkdir docs
mkdir src
mkdir web
mkdir web/WEB-INF
cvs or svn or git ... &lt;-- Add this structure to the appropriate repository
</code></pre></div>

<p>To verify that the project was created correctly in the source code control
repository, you may wish to check out the project to a separate directory and
confirm that all the expected contents are present.</p>

<p>Next, you will need to create and check in an initial version of the
<code>build.xml</code> script to be used for development.  For getting
started quickly and easily, base your <code>build.xml</code> on the
<a href="build.xml.txt">basic build.xml file</a>, included with this manual,
or code it from scratch.</p>
<div class="codeBox"><pre><code>cd {my home directory}
cd myapp
emacs build.xml     &lt;-- if you want a real editor :-)
cvs or svn or git ... &lt;-- Add this file to the repository
</code></pre></div>

<p>The next step is to customize the Ant <em>properties</em> that are
named in the <code>build.xml</code> script.  This is done by creating a
file named <code>build.properties</code> in your project's top-level
directory.  The supported properties are listed in the comments inside
the sample <code>build.xml</code> script.  At a minimum, you will generally
need to define the <code>catalina.home</code> property defining where
Tomcat is installed, and the manager application username and password.
You might end up with something like this:</p>
<div class="codeBox"><pre><code># Context path to install this application on
app.path=/hello

# Tomcat installation directory
catalina.home=/usr/local/apache-tomcat-9.0

# Manager webapp username and password
manager.username=myusername
manager.password=mypassword</code></pre></div>

<p>In general, you will <strong>not</strong> want to check the
<code>build.properties</code> file in to the source code control repository,
because it is unique to each developer's environment.</p>

<p>Now, create the initial version of the web application deployment
descriptor.  You can base <code>web.xml</code> on the
<a href="web.xml.txt">basic web.xml file</a>, or code it from scratch.</p>
<div class="codeBox"><pre><code>cd {my home directory}
cd myapp/web/WEB-INF
emacs web.xml
cvs or svn or git ... &lt;-- Add this file to the repository
</code></pre></div>

Note that this is only an example web.xml file.  The full definition
of the deployment descriptor file is in the
<a href="https://cwiki.apache.org/confluence/display/TOMCAT/Specifications">Servlet Specification.</a>

</div></div>


<div class="subsection"><h4 id="Edit_Source_Code_and_Pages">Edit Source Code and Pages</h4><div class="text">

<p>The edit/build/test tasks will generally be your most common activities
during development and maintenance.  The following general principles apply.
As described in <a href="source.html">Source Organization</a>, newly created
source files should be located in the appropriate subdirectory, under your
project source directory.</p>

<p>You should regularly refresh your development directory to reflect the
work performed by other developers.</p>

<p>To create a new file, go to the appropriate directory and create the file.
When you are satisfied with its contents (after building and testing is
successful), add the new file to the repository. For example, to create a new
JSP page:</p>
<div class="codeBox"><pre><code>cd {my home directory}
cd myapp/web        &lt;-- Ultimate destination is document root
emacs mypage.jsp
... build and test the application ...
cvs or svn or git ... &lt;-- Add this file to the repository
</code></pre></div>

<p>Java source code that is defined in packages must be organized in a directory
hierarchy (under the <strong>src/</strong> subdirectory) that matches the
package names.  For example, a Java class named
<code>com.mycompany.mypackage.MyClass.java</code> should be stored in file
<code>src/com/mycompany/mypackage/MyClass.java</code>.
Whenever you create a new file, don't forget to add it to the source code
control system.</p>

<p>To edit an existing source file, you will generally just start editing
and testing, then commit the changed file when everything works.</p>

</div></div>


<div class="subsection"><h4 id="Build_the_Web_Application">Build the Web Application</h4><div class="text">

<p>When you are ready to compile the application, issue the following
commands (generally, you will want a shell window open that is set to
the project source directory, so that only the last command is needed):</p>
<div class="codeBox"><pre><code>cd {my home directory}
cd myapp        &lt;-- Normally leave a window open here
ant</code></pre></div>

<p>The Ant tool will be execute the default "compile" target in your
<code>build.xml</code> file, which will compile any new or updated Java
code.  If this is the first time you compile after a "build clean",
it will cause everything to be recompiled.</p>

<p>To force the recompilation of your entire application, do this instead:</p>
<div class="codeBox"><pre><code>cd {my home directory}
cd myapp
ant all</code></pre></div>

<p>This is a very good habit immediately before checking in changes, to
make sure that you have not introduced any subtle problems that Javac's
conditional checking did not catch.</p>

</div></div>


<div class="subsection"><h4 id="Test_Your_Web_Application">Test Your Web Application</h4><div class="text">

<p>To test your application, you will want to install it under Tomcat.  The
quickest way to do that is to use the custom Ant tasks that are included in
the sample <code>build.xml</code> script.  Using these commands might follow
a pattern like this:</p>
<ul>
<li><p><em>Start Tomcat if needed</em>.  If Tomcat is not already running,
    you will need to start it in the usual way.
    </p></li>
<li><p><em>Compile your application</em>.  Use the <code>ant compile</code>
    command (or just <code>ant</code>, since this is the default).  Make
    sure that there are no compilation errors.
    </p></li>
<li><p><em>Install the application</em>.  Use the <code>ant install</code>
    command.  This tells Tomcat to immediately start running your app on
    the context path defined in the <code>app.path</code> build property.
    Tomcat does <strong>NOT</strong> have to be restarted for this to
    take effect.
    </p></li>
<li><p><em>Test the application</em>.  Using your browser or other testing
    tools, test the functionality of your application.
    </p></li>
<li><p><em>Modify and rebuild as needed</em>.  As you discover that changes
    are required, make those changes in the original <strong>source</strong>
    files, not in the output build directory, and re-issue the
    <code>ant compile</code> command.  This ensures that your changes will be
    available to be saved (via your chosen source code control system) later on
    -- the output build directory is deleted and recreated as necessary.
    </p></li>
<li><p><em>Reload the application</em>.  Tomcat will recognize changes in
    JSP pages automatically, but it will continue to use the old versions
    of any servlet or JavaBean classes until the application is reloaded.
    You can trigger this by executing the <code>ant reload</code> command.
    </p></li>
<li><p><em>Remove the application when you are done</em>.  When you are through
    working on this application, you can remove it from live execution by
    running the <code>ant remove</code> command.
    </p></li>
</ul>

<p>Do not forget to commit your changes to the source code repository when
you have completed your testing!</p>

</div></div>


<div class="subsection"><h4 id="Creating_a_Release">Creating a Release</h4><div class="text">

<p>When you are through adding new functionality, and you've tested everything
(you DO test, don't you :-), it is time to create the distributable version
of your web application that can be deployed on the production server.  The
following general steps are required:</p>
<ul>
<li><p>Issue the command <code>ant all</code> from the project source
    directory, to rebuild everything from scratch one last time.
    </p></li>
<li><p>Use the source code control system to tag the current state of the code
    to create an identifier for all of the source files utilized to create this
    release.  This allows you to reliably reconstruct a release (from sources)
    at a later time.
    </p></li>
<li><p>Issue the command <code>ant dist</code> to create a distributable
    web application archive (WAR) file, as well as a JAR file containing
    the corresponding source code.
    </p></li>
<li><p>Package the contents of the <code>dist</code> directory using the
    <strong>tar</strong> or <strong>zip</strong> utility, according to
    the standard release procedures used by your organization.
    </p></li>
</ul>

</div></div>


</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>