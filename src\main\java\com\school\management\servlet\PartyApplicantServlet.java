package com.school.management.servlet;

import com.school.management.entity.PartyApplicant;
import com.school.management.service.PartyApplicantService;
import com.school.management.service.impl.PartyApplicantServiceImpl;
import com.school.management.service.BaseService;
import com.school.management.util.JsonUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 入党申请人Servlet
 * 处理入党申请人相关的HTTP请求
 */
@WebServlet("/api/applicant/*")
public class PartyApplicantServlet extends HttpServlet {
    
    private PartyApplicantService applicantService = new PartyApplicantServiceImpl();
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                // 获取所有申请人或分页查询
                handleGetAll(request, response);
            } else if (pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    // 根据ID获取申请人
                    int id = Integer.parseInt(pathParts[1]);
                    handleGetById(id, response);
                }
            }
        } catch (Exception e) {
            handleError(response, "查询失败: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        try {
            // 添加新申请人
            PartyApplicant applicant = parseApplicantFromRequest(request);
            Integer id = applicantService.add(applicant);
            
            if (id != null) {
                applicant.setId(id);
                writeSuccessResponse(response, applicant, "添加成功");
            } else {
                handleError(response, "添加失败");
            }
        } catch (Exception e) {
            handleError(response, "添加失败: " + e.getMessage());
        }
    }
    
    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        try {
            // 更新申请人
            PartyApplicant applicant = parseApplicantFromRequest(request);
            boolean success = applicantService.update(applicant);
            
            if (success) {
                writeSuccessResponse(response, applicant, "更新成功");
            } else {
                handleError(response, "更新失败");
            }
        } catch (Exception e) {
            handleError(response, "更新失败: " + e.getMessage());
        }
    }
    
    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo != null && pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    // 根据ID删除申请人
                    int id = Integer.parseInt(pathParts[1]);
                    boolean success = applicantService.deleteById(id);
                    
                    if (success) {
                        writeSuccessResponse(response, null, "删除成功");
                    } else {
                        handleError(response, "删除失败");
                    }
                }
            }
        } catch (Exception e) {
            handleError(response, "删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理获取所有申请人的请求
     */
    private void handleGetAll(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String pageStr = request.getParameter("page");
        String sizeStr = request.getParameter("size");
        
        if (pageStr != null && sizeStr != null) {
            // 分页查询
            int page = Integer.parseInt(pageStr);
            int size = Integer.parseInt(sizeStr);
            
            BaseService.PageResult<PartyApplicant> result =
                applicantService.getByPage(page, size);
            writeSuccessResponse(response, result, "查询成功");
        } else {
            // 查询所有
            List<PartyApplicant> applicants = applicantService.getAll();
            writeSuccessResponse(response, applicants, "查询成功");
        }
    }
    
    /**
     * 处理根据ID获取申请人的请求
     */
    private void handleGetById(int id, HttpServletResponse response) throws IOException {
        PartyApplicant applicant = applicantService.getById(id);
        if (applicant != null) {
            writeSuccessResponse(response, applicant, "查询成功");
        } else {
            handleError(response, "申请人不存在");
        }
    }
    
    /**
     * 从请求中解析申请人对象
     */
    private PartyApplicant parseApplicantFromRequest(HttpServletRequest request) 
            throws ParseException {
        
        PartyApplicant applicant = new PartyApplicant();
        
        String idStr = request.getParameter("id");
        if (idStr != null && !idStr.trim().isEmpty()) {
            applicant.setId(Integer.parseInt(idStr));
        }
        
        applicant.setName(request.getParameter("name"));
        applicant.setGender(request.getParameter("gender"));
        
        String birthDateStr = request.getParameter("birthDate");
        if (birthDateStr != null && !birthDateStr.trim().isEmpty()) {
            applicant.setBirthDate(dateFormat.parse(birthDateStr));
        }
        
        applicant.setIdCard(request.getParameter("idCard"));
        applicant.setPhone(request.getParameter("phone"));
        applicant.setEmail(request.getParameter("email"));
        applicant.setDepartment(request.getParameter("department"));
        applicant.setMajor(request.getParameter("major"));
        applicant.setClassName(request.getParameter("className"));
        applicant.setStudentId(request.getParameter("studentId"));
        
        String applicationDateStr = request.getParameter("applicationDate");
        if (applicationDateStr != null && !applicationDateStr.trim().isEmpty()) {
            applicant.setApplicationDate(dateFormat.parse(applicationDateStr));
        }
        
        applicant.setPoliticalStatus(request.getParameter("politicalStatus"));
        applicant.setHometown(request.getParameter("hometown"));
        applicant.setFamilyBackground(request.getParameter("familyBackground"));
        applicant.setPersonalResume(request.getParameter("personalResume"));
        applicant.setApplicationReason(request.getParameter("applicationReason"));
        applicant.setStatus(request.getParameter("status"));
        applicant.setRemarks(request.getParameter("remarks"));
        
        return applicant;
    }
    
    /**
     * 写入成功响应
     */
    private void writeSuccessResponse(HttpServletResponse response, Object data, String message)
            throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        String json = JsonUtil.createSuccessResponse(data, message);
        PrintWriter out = response.getWriter();
        out.write(json);
        out.flush();
    }

    /**
     * 处理错误响应
     */
    private void handleError(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);

        String json = JsonUtil.createErrorResponse(message);
        PrintWriter out = response.getWriter();
        out.write(json);
        out.flush();
    }
}
