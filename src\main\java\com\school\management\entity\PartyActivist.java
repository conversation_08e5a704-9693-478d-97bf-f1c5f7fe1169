package com.school.management.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 入党积极分子实体类
 */
public class PartyActivist {
    
    private Integer id;                    // 主键ID
    private Integer applicantId;           // 申请人ID（关联入党申请人）
    private String name;                   // 姓名
    private String gender;                 // 性别
    private Date birthDate;                // 出生日期
    private Integer age;                   // 年龄
    private String idCard;                 // 身份证号
    private String nativePlace;           // 户籍地
    private String address;                // 地址
    private String phone;                  // 联系电话
    private String grade;                  // 年级
    private Boolean isLeagueMember;        // 是否为共青团员
    private Date applicationDate;          // 申请日期
    private Date activistDate;             // 确定积极分子日期
    private String branchSecretary;        // 支部书记
    private Boolean hasLeagueRecommendation; // 是否经过共青团推优
    private String status;                 // 状态
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public PartyActivist() {}

    // 从入党申请人转换的构造函数
    public PartyActivist(PartyApplicant applicant, Date activistDate, String branchSecretary, <PERSON>olean hasLeagueRecommendation) {
        this.applicantId = applicant.getId();
        this.name = applicant.getName();
        this.gender = applicant.getGender();
        this.birthDate = applicant.getBirthDate();
        this.age = applicant.getAge();
        this.idCard = applicant.getIdCard();
        this.nativePlace = applicant.getNativePlace();
        this.address = applicant.getAddress();
        this.phone = applicant.getPhone();
        this.grade = applicant.getGrade();
        this.isLeagueMember = applicant.getIsLeagueMember();
        this.applicationDate = applicant.getApplicationDate();
        this.activistDate = activistDate;
        this.branchSecretary = branchSecretary;
        this.hasLeagueRecommendation = hasLeagueRecommendation;
        this.status = "积极分子";
    }

    // 全参构造函数
    public PartyActivist(Integer id, Integer applicantId, String name, String gender, Date birthDate, Integer age,
                        String idCard, String nativePlace, String address, String phone, String grade,
                        Boolean isLeagueMember, Date applicationDate, Date activistDate, String branchSecretary,
                        Boolean hasLeagueRecommendation, String status, String remarks,
                        Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.applicantId = applicantId;
        this.name = name;
        this.gender = gender;
        this.birthDate = birthDate;
        this.age = age;
        this.idCard = idCard;
        this.nativePlace = nativePlace;
        this.address = address;
        this.phone = phone;
        this.grade = grade;
        this.isLeagueMember = isLeagueMember;
        this.applicationDate = applicationDate;
        this.activistDate = activistDate;
        this.branchSecretary = branchSecretary;
        this.hasLeagueRecommendation = hasLeagueRecommendation;
        this.status = status;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public Integer getApplicantId() { return applicantId; }
    public void setApplicantId(Integer applicantId) { this.applicantId = applicantId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public Date getBirthDate() { return birthDate; }
    public void setBirthDate(Date birthDate) { this.birthDate = birthDate; }

    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }

    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) { this.idCard = idCard; }

    public String getNativePlace() { return nativePlace; }
    public void setNativePlace(String nativePlace) { this.nativePlace = nativePlace; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getGrade() { return grade; }
    public void setGrade(String grade) { this.grade = grade; }

    public Boolean getIsLeagueMember() { return isLeagueMember; }
    public void setIsLeagueMember(Boolean isLeagueMember) { this.isLeagueMember = isLeagueMember; }

    public Date getApplicationDate() { return applicationDate; }
    public void setApplicationDate(Date applicationDate) { this.applicationDate = applicationDate; }

    public Date getActivistDate() { return activistDate; }
    public void setActivistDate(Date activistDate) { this.activistDate = activistDate; }

    public String getBranchSecretary() { return branchSecretary; }
    public void setBranchSecretary(String branchSecretary) { this.branchSecretary = branchSecretary; }

    public Boolean getHasLeagueRecommendation() { return hasLeagueRecommendation; }
    public void setHasLeagueRecommendation(Boolean hasLeagueRecommendation) {
        this.hasLeagueRecommendation = hasLeagueRecommendation;
    }

    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }
    
    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "PartyActivist{" +
                "id=" + id +
                ", applicantId=" + applicantId +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", age=" + age +
                ", idCard='" + idCard + '\'' +
                ", nativePlace='" + nativePlace + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", grade='" + grade + '\'' +
                ", isLeagueMember=" + isLeagueMember +
                ", applicationDate=" + applicationDate +
                ", activistDate=" + activistDate +
                ", branchSecretary='" + branchSecretary + '\'' +
                ", hasLeagueRecommendation=" + hasLeagueRecommendation +
                ", status='" + status + '\'' +
                ", remarks='" + remarks + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
