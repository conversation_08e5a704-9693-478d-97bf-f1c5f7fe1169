<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学校党员信息管理系统</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }

        .header h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* 导航样式 */
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav li {
            margin: 0 15px;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 主内容区域 */
        .main-content {
            padding: 30px;
            min-height: 600px;
        }

        .module {
            display: none;
        }

        .module.active {
            display: block;
        }

        /* 统计卡片样式 */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .stat-card span {
            font-size: 2.5em;
            font-weight: bold;
            display: block;
        }

        /* 表格样式 */
        .table-container {
            margin-top: 20px;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 表单样式 */
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            overflow: auto;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.5em;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ddd;
        }

        .modal-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #ddd;
            text-align: right;
            border-radius: 0 0 10px 10px;
        }

        /* 表单行样式 */
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-row .form-group.full-width {
            flex: 100%;
        }

        /* 必填字段标识 */
        .required {
            color: #ff4b2b;
        }

        /* 模块头部样式 */
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }

        .module-header h2 {
            color: #667eea;
            margin: 0;
        }

        .module-actions {
            display: flex;
            gap: 10px;
        }

        /* 搜索容器样式 */
        .search-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form .form-row {
            align-items: end;
        }

        .search-form .form-group {
            margin-bottom: 0;
        }

        /* 状态徽章样式 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
        }

        .status-待审核, .status-政治审查中, .status-培训中 {
            background-color: #ffc107;
            color: #212529;
        }

        .status-已通过, .status-通过, .status-准备入党, .status-预备期中, .status-正常 {
            background-color: #28a745;
            color: white;
        }

        .status-已拒绝, .status-不通过, .status-已取消, .status-已作废 {
            background-color: #dc3545;
            color: white;
        }

        .status-已转预备党员, .status-已转正式党员, .status-已开具, .status-已接收 {
            background-color: #17a2b8;
            color: white;
        }

        .status-延长预备期, .status-已过期 {
            background-color: #fd7e14;
            color: white;
        }

        /* 分页容器样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        /* 批量操作样式 */
        .batch-actions {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            display: flex;
            gap: 10px;
        }

        /* 小按钮样式 */
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            margin: 2px;
        }

        /* 文本居中 */
        .text-center {
            text-align: center;
        }

        /* 图标样式（如果需要的话） */
        .icon-plus::before { content: '+'; }
        .icon-export::before { content: '↓'; }

        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .message-success {
            background-color: #28a745;
        }

        .message-error {
            background-color: #dc3545;
        }

        .message-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .message-info {
            background-color: #17a2b8;
        }

        .message-close {
            background: none;
            border: none;
            color: inherit;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            margin-left: 15px;
        }

        .message-close:hover {
            opacity: 0.7;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
                align-items: center;
            }

            .nav li {
                margin: 5px 0;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 15px;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 2% auto;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .module-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .search-form .form-row {
                flex-direction: column;
            }

            .pagination-container {
                flex-direction: column;
                gap: 15px;
            }

            .batch-actions {
                flex-wrap: wrap;
            }
        }

    </style>


</head>
<body>
    <div class="container">
        <header class="header">
            <h1>学校党员信息管理系统</h1>
            <div class="header-info">
                <span>当前时间: <%= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) %></span>
                <span>欢迎使用</span>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="<%=request.getContextPath()%>/applicant-management.jsp">入党申请人管理</a></li>
                    <li><a href="#" onclick="showModule('activist')">入党积极分子</a></li>
                    <li><a href="#" onclick="showModule('development')">发展对象</a></li>
                    <li><a href="#" onclick="showModule('probationary')">预备党员</a></li>
                    <li><a href="#" onclick="showModule('formal')">正式党员</a></li>
                    <li><a href="#" onclick="showModule('transfer')">组织关系介绍信</a></li>
                </ul>
            </nav>
        </header>

        <main class="main-content">
            <div id="welcome" class="module active">
                <h2>欢迎使用学校党员信息管理系统</h2>
                <p>请选择上方菜单进行操作</p>
                <p class="system-info">系统版本: v1.0 | 服务器时间: <%= new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(new Date()) %></p>

                <div class="quick-links" style="margin: 20px 0; text-align: center;">
                    <h3>快速链接</h3>
                    <a href="<%=request.getContextPath()%>/test.jsp" class="quick-link-btn">功能测试</a>
                    <a href="<%=request.getContextPath()%>/db-test.jsp" class="quick-link-btn">数据库测试</a>
                    <a href="<%=request.getContextPath()%>/java-error-check.jsp" class="quick-link-btn">Java错误检查</a>
                    <a href="<%=request.getContextPath()%>/test-fixes.jsp" class="quick-link-btn">Bug修复验证</a>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <h3>入党申请人</h3>
                        <span id="applicant-count">0</span>
                        <small>人</small>
                    </div>
                    <div class="stat-card">
                        <h3>入党积极分子</h3>
                        <span id="activist-count">0</span>
                        <small>人</small>
                    </div>
                    <div class="stat-card">
                        <h3>发展对象</h3>
                        <span id="development-count">0</span>
                        <small>人</small>
                    </div>
                    <div class="stat-card">
                        <h3>预备党员</h3>
                        <span id="probationary-count">0</span>
                        <small>人</small>
                    </div>
                    <div class="stat-card">
                        <h3>正式党员</h3>
                        <span id="formal-count">0</span>
                        <small>人</small>
                    </div>
                </div>

                <div class="recent-activities">
                    <h3>系统状态</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <span class="activity-time"><%= new SimpleDateFormat("HH:mm").format(new Date()) %></span>
                            <span class="activity-desc">系统正常运行中</span>
                            <span class="activity-status status-success">正常</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-time">启动时间</span>
                            <span class="activity-desc">数据库连接池已初始化</span>
                            <span class="activity-status status-success">就绪</span>
                        </div>
                        <div class="activity-item">
                            <span class="activity-time">版本信息</span>
                            <span class="activity-desc">学校党员信息管理系统 v1.0</span>
                            <span class="activity-status status-info">稳定版</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 各个模块的内容将通过JavaScript动态加载 -->
            <div id="module-content" style="display: none;"></div>
        </main>
    </div>

    <!-- 页面底部信息 -->
    <footer class="footer">
        <p>&copy; 2024 学校党员信息管理系统 | 
           服务器: <%= request.getServerName() %>:<%= request.getServerPort() %> | 
           应用路径: <%= request.getContextPath() %>
        </p>
    </footer>

    <script>
        // 设置全局上下文路径
        window.contextPath = '<%=request.getContextPath()%>';
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JSP页面加载完成');
            console.log('上下文路径:', window.contextPath);
            
            // 加载统计数据
            loadStatistics();
            
            // 每30秒更新一次时间显示
            setInterval(updateTime, 30000);
        });
        
        // 更新时间显示
        function updateTime() {
            // 这里可以通过AJAX获取服务器时间
            // 暂时使用客户端时间
            const now = new Date();
            const timeStr = now.getFullYear() + '年' + 
                          (now.getMonth() + 1).toString().padStart(2, '0') + '月' + 
                          now.getDate().toString().padStart(2, '0') + '日 ' +
                          now.getHours().toString().padStart(2, '0') + ':' + 
                          now.getMinutes().toString().padStart(2, '0') + ':' + 
                          now.getSeconds().toString().padStart(2, '0');
            
            const timeElements = document.querySelectorAll('.system-info');
            timeElements.forEach(el => {
                if (el.textContent.includes('服务器时间')) {
                    el.innerHTML = el.innerHTML.replace(/服务器时间: .*/, '服务器时间: ' + timeStr);
                }
            });
        }
    </script>
    <script src="<%=request.getContextPath()%>/js/main.js"></script>
</body>
</html>
