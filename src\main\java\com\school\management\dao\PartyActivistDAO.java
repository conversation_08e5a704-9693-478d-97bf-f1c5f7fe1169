package com.school.management.dao;

import com.school.management.entity.PartyActivist;
import java.util.List;
import java.util.Map;

/**
 * 入党积极分子数据访问接口
 */
public interface PartyActivistDAO extends BaseDAO<PartyActivist> {

    /**
     * 插入积极分子并返回完整对象
     * @param activist 积极分子对象
     * @return 插入成功返回带ID的对象，失败返回null
     */
    PartyActivist insertAndReturn(PartyActivist activist);
    
    /**
     * 根据申请人ID查询积极分子
     * @param applicantId 申请人ID
     * @return 积极分子对象，如果不存在返回null
     */
    PartyActivist selectByApplicantId(Integer applicantId);
    
    /**
     * 根据身份证号查询积极分子
     * @param idCard 身份证号
     * @return 积极分子对象，如果不存在返回null
     */
    PartyActivist selectByIdCard(String idCard);
    
    /**
     * 根据姓名模糊查询积极分子
     * @param name 姓名关键字
     * @return 积极分子列表
     */
    List<PartyActivist> selectByNameLike(String name);
    
    /**
     * 根据年级查询积极分子
     * @param grade 年级
     * @return 积极分子列表
     */
    List<PartyActivist> selectByGrade(String grade);
    
    /**
     * 根据状态查询积极分子
     * @param status 状态
     * @return 积极分子列表
     */
    List<PartyActivist> selectByStatus(String status);
    
    /**
     * 根据支部书记查询积极分子
     * @param branchSecretary 支部书记
     * @return 积极分子列表
     */
    List<PartyActivist> selectByBranchSecretary(String branchSecretary);
    
    /**
     * 根据是否共青团推优查询积极分子
     * @param hasLeagueRecommendation 是否共青团推优
     * @return 积极分子列表
     */
    List<PartyActivist> selectByLeagueRecommendation(Boolean hasLeagueRecommendation);
    
    /**
     * 根据确定积极分子日期范围查询
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 积极分子列表
     */
    List<PartyActivist> selectByActivistDateRange(String startDate, String endDate);
    
    /**
     * 根据多个条件查询积极分子（分页）
     * @param conditions 查询条件Map
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 积极分子列表
     */
    List<PartyActivist> selectByConditionsWithPage(Map<String, Object> conditions, int offset, int limit);
    
    /**
     * 根据多个条件统计积极分子数量
     * @param conditions 查询条件Map
     * @return 总数量
     */
    int countByConditions(Map<String, Object> conditions);
    
    /**
     * 根据多个条件查询积极分子
     * 支持的条件：name, idCard, grade, status, branchSecretary, hasLeagueRecommendation
     * @param name 姓名（模糊查询）
     * @param grade 年级
     * @param status 状态
     * @param branchSecretary 支部书记
     * @param hasLeagueRecommendation 是否共青团推优
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 积极分子列表
     */
    List<PartyActivist> selectByMultipleConditionsWithPage(String name, String grade, String status,
                                                           String branchSecretary, Boolean hasLeagueRecommendation,
                                                           int offset, int limit);
    
    /**
     * 根据多个条件统计积极分子数量
     * @param name 姓名（模糊查询）
     * @param grade 年级
     * @param status 状态
     * @param branchSecretary 支部书记
     * @param hasLeagueRecommendation 是否共青团推优
     * @return 总数量
     */
    int countByMultipleConditions(String name, String grade, String status,
                                 String branchSecretary, Boolean hasLeagueRecommendation);
    
    /**
     * 检查申请人是否已经是积极分子
     * @param applicantId 申请人ID
     * @return 是否已经是积极分子
     */
    boolean existsByApplicantId(Integer applicantId);
    
    /**
     * 检查身份证号是否已存在
     * @param idCard 身份证号
     * @return 是否已存在
     */
    boolean existsByIdCard(String idCard);
    
    /**
     * 根据申请人ID删除积极分子记录
     * @param applicantId 申请人ID
     * @return 是否删除成功
     */
    boolean deleteByApplicantId(Integer applicantId);
}
