package com.school.management.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 发展对象实体类
 */
public class DevelopmentTarget {
    
    private Integer id;                    // 主键ID
    private Integer activistId;            // 积极分子ID
    private String name;                   // 姓名
    private String gender;                 // 性别
    private Date birthDate;                // 出生日期
    private String idCard;                 // 身份证号
    private String phone;                  // 联系电话
    private String email;                  // 邮箱
    private String department;             // 所在院系
    private String major;                  // 专业
    private String className;              // 班级
    private String studentId;              // 学号
    private Date determinationDate;        // 确定为发展对象日期
    private Date politicalReviewDate;      // 政治审查日期
    private String politicalReviewResult;  // 政治审查结果
    private Date trainingCompletionDate;   // 培训完成日期
    private BigDecimal trainingScore;      // 培训成绩
    private String recommendationLetter;   // 推荐信
    private String personalStatement;      // 个人陈述
    private String status;                 // 状态
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public DevelopmentTarget() {}
    
    // 全参构造函数
    public DevelopmentTarget(Integer id, Integer activistId, String name, String gender, Date birthDate,
                           String idCard, String phone, String email, String department, String major,
                           String className, String studentId, Date determinationDate, Date politicalReviewDate,
                           String politicalReviewResult, Date trainingCompletionDate, BigDecimal trainingScore,
                           String recommendationLetter, String personalStatement, String status, String remarks,
                           Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.activistId = activistId;
        this.name = name;
        this.gender = gender;
        this.birthDate = birthDate;
        this.idCard = idCard;
        this.phone = phone;
        this.email = email;
        this.department = department;
        this.major = major;
        this.className = className;
        this.studentId = studentId;
        this.determinationDate = determinationDate;
        this.politicalReviewDate = politicalReviewDate;
        this.politicalReviewResult = politicalReviewResult;
        this.trainingCompletionDate = trainingCompletionDate;
        this.trainingScore = trainingScore;
        this.recommendationLetter = recommendationLetter;
        this.personalStatement = personalStatement;
        this.status = status;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public Integer getActivistId() { return activistId; }
    public void setActivistId(Integer activistId) { this.activistId = activistId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }
    
    public Date getBirthDate() { return birthDate; }
    public void setBirthDate(Date birthDate) { this.birthDate = birthDate; }
    
    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) { this.idCard = idCard; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public String getMajor() { return major; }
    public void setMajor(String major) { this.major = major; }
    
    public String getClassName() { return className; }
    public void setClassName(String className) { this.className = className; }
    
    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }
    
    public Date getDeterminationDate() { return determinationDate; }
    public void setDeterminationDate(Date determinationDate) { this.determinationDate = determinationDate; }
    
    public Date getPoliticalReviewDate() { return politicalReviewDate; }
    public void setPoliticalReviewDate(Date politicalReviewDate) { this.politicalReviewDate = politicalReviewDate; }
    
    public String getPoliticalReviewResult() { return politicalReviewResult; }
    public void setPoliticalReviewResult(String politicalReviewResult) { this.politicalReviewResult = politicalReviewResult; }
    
    public Date getTrainingCompletionDate() { return trainingCompletionDate; }
    public void setTrainingCompletionDate(Date trainingCompletionDate) { this.trainingCompletionDate = trainingCompletionDate; }
    
    public BigDecimal getTrainingScore() { return trainingScore; }
    public void setTrainingScore(BigDecimal trainingScore) { this.trainingScore = trainingScore; }
    
    public String getRecommendationLetter() { return recommendationLetter; }
    public void setRecommendationLetter(String recommendationLetter) { this.recommendationLetter = recommendationLetter; }
    
    public String getPersonalStatement() { return personalStatement; }
    public void setPersonalStatement(String personalStatement) { this.personalStatement = personalStatement; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }
    
    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "DevelopmentTarget{" +
                "id=" + id +
                ", activistId=" + activistId +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", idCard='" + idCard + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", department='" + department + '\'' +
                ", major='" + major + '\'' +
                ", className='" + className + '\'' +
                ", studentId='" + studentId + '\'' +
                ", determinationDate=" + determinationDate +
                ", politicalReviewDate=" + politicalReviewDate +
                ", politicalReviewResult='" + politicalReviewResult + '\'' +
                ", trainingCompletionDate=" + trainingCompletionDate +
                ", trainingScore=" + trainingScore +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
