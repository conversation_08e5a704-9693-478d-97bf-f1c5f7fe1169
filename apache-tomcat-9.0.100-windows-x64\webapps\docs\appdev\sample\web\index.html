<!DOCTYPE html><!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<html>
<head>
<meta charset="UTF-8" />
<title>Sample "Hello, World" Application</title>
</head>
<body>

<div style="float: left; padding: 10px;">
<img src="images/tomcat.gif" alt="" />
</div>
<h1>Sample "Hello, World" Application</h1>
<p>This is the home page for a sample application used to illustrate the
source directory organization of a web application utilizing the principles
outlined in the Application Developer's Guide.

<p>To prove that they work, you can execute either of the following links:</p>
<ul>
<li>To a <a href="hello.jsp">JSP page</a>.</li>
<li>To a <a href="hello">servlet</a>.</li>
</ul>

</body>
</html>
