package com.school.management.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 正式党员实体类
 */
public class FormalMember {
    
    private Integer id;                    // 主键ID
    private Integer probationaryId;        // 预备党员ID
    private String name;                   // 姓名
    private String gender;                 // 性别
    private Date birthDate;                // 出生日期
    private String idCard;                 // 身份证号
    private String phone;                  // 联系电话
    private String email;                  // 邮箱
    private String department;             // 所在院系
    private String major;                  // 专业
    private String className;              // 班级
    private String studentId;              // 学号
    private Date admissionDate;            // 入党日期
    private Date formalDate;               // 转正日期
    private String partyBranch;            // 所在党支部
    private String partyPosition;          // 党内职务
    private String annualEvaluation;       // 年度评议
    private String honorsAwards;           // 荣誉奖励
    private String volunteerActivities;    // 志愿活动记录
    private String continuingEducation;    // 继续教育记录
    private String status;                 // 党员状态
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public FormalMember() {}
    
    // 全参构造函数
    public FormalMember(Integer id, Integer probationaryId, String name, String gender, Date birthDate,
                       String idCard, String phone, String email, String department, String major,
                       String className, String studentId, Date admissionDate, Date formalDate,
                       String partyBranch, String partyPosition, String annualEvaluation, String honorsAwards,
                       String volunteerActivities, String continuingEducation, String status, String remarks,
                       Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.probationaryId = probationaryId;
        this.name = name;
        this.gender = gender;
        this.birthDate = birthDate;
        this.idCard = idCard;
        this.phone = phone;
        this.email = email;
        this.department = department;
        this.major = major;
        this.className = className;
        this.studentId = studentId;
        this.admissionDate = admissionDate;
        this.formalDate = formalDate;
        this.partyBranch = partyBranch;
        this.partyPosition = partyPosition;
        this.annualEvaluation = annualEvaluation;
        this.honorsAwards = honorsAwards;
        this.volunteerActivities = volunteerActivities;
        this.continuingEducation = continuingEducation;
        this.status = status;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public Integer getProbationaryId() { return probationaryId; }
    public void setProbationaryId(Integer probationaryId) { this.probationaryId = probationaryId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }
    
    public Date getBirthDate() { return birthDate; }
    public void setBirthDate(Date birthDate) { this.birthDate = birthDate; }
    
    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) { this.idCard = idCard; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public String getMajor() { return major; }
    public void setMajor(String major) { this.major = major; }
    
    public String getClassName() { return className; }
    public void setClassName(String className) { this.className = className; }
    
    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }
    
    public Date getAdmissionDate() { return admissionDate; }
    public void setAdmissionDate(Date admissionDate) { this.admissionDate = admissionDate; }
    
    public Date getFormalDate() { return formalDate; }
    public void setFormalDate(Date formalDate) { this.formalDate = formalDate; }
    
    public String getPartyBranch() { return partyBranch; }
    public void setPartyBranch(String partyBranch) { this.partyBranch = partyBranch; }
    
    public String getPartyPosition() { return partyPosition; }
    public void setPartyPosition(String partyPosition) { this.partyPosition = partyPosition; }
    
    public String getAnnualEvaluation() { return annualEvaluation; }
    public void setAnnualEvaluation(String annualEvaluation) { this.annualEvaluation = annualEvaluation; }
    
    public String getHonorsAwards() { return honorsAwards; }
    public void setHonorsAwards(String honorsAwards) { this.honorsAwards = honorsAwards; }
    
    public String getVolunteerActivities() { return volunteerActivities; }
    public void setVolunteerActivities(String volunteerActivities) { this.volunteerActivities = volunteerActivities; }
    
    public String getContinuingEducation() { return continuingEducation; }
    public void setContinuingEducation(String continuingEducation) { this.continuingEducation = continuingEducation; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }
    
    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "FormalMember{" +
                "id=" + id +
                ", probationaryId=" + probationaryId +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", idCard='" + idCard + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", department='" + department + '\'' +
                ", major='" + major + '\'' +
                ", className='" + className + '\'' +
                ", studentId='" + studentId + '\'' +
                ", admissionDate=" + admissionDate +
                ", formalDate=" + formalDate +
                ", partyBranch='" + partyBranch + '\'' +
                ", partyPosition='" + partyPosition + '\'' +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
