<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试 - 学校党员信息管理系统</title>
    <link rel="stylesheet" href="<%=request.getContextPath()%>/css/style.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .server-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 功能测试页面</h1>
        
        <div class="server-info">
            <h3>📊 服务器信息</h3>
            <p><strong>服务器名称:</strong> <%= request.getServerName() %></p>
            <p><strong>服务器端口:</strong> <%= request.getServerPort() %></p>
            <p><strong>应用上下文:</strong> <%= request.getContextPath() %></p>
            <p><strong>请求协议:</strong> <%= request.getProtocol() %></p>
            <p><strong>请求方法:</strong> <%= request.getMethod() %></p>
            <p><strong>客户端IP:</strong> <%= request.getRemoteAddr() %></p>
            <p><strong>当前时间:</strong> <%= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) %></p>
            <p><strong>JSP版本:</strong> <%= application.getMajorVersion() %>.<%= application.getMinorVersion() %></p>
        </div>

        <div class="test-section">
            <h3>🔌 数据库连接测试</h3>
            <p>测试与MySQL数据库的连接是否正常</p>
            <button class="test-btn" onclick="testDatabaseConnection()">测试数据库连接</button>
            <div id="dbResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📡 API接口测试</h3>
            <p>测试后端API接口的响应</p>
            <button class="test-btn" onclick="testApiEndpoints()">测试API接口</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 表单提交测试</h3>
            <p>测试表单数据的提交和验证</p>
            <form id="testForm" onsubmit="return testFormSubmission(event)">
                <div style="margin-bottom: 10px;">
                    <label>姓名: </label>
                    <input type="text" name="name" placeholder="请输入姓名" required>
                </div>
                <div style="margin-bottom: 10px;">
                    <label>邮箱: </label>
                    <input type="email" name="email" placeholder="请输入邮箱">
                </div>
                <div style="margin-bottom: 10px;">
                    <label>部门: </label>
                    <select name="department">
                        <option value="">请选择部门</option>
                        <option value="计算机学院">计算机学院</option>
                        <option value="电子工程学院">电子工程学院</option>
                        <option value="管理学院">管理学院</option>
                    </select>
                </div>
                <button type="submit" class="test-btn">提交测试</button>
            </form>
            <div id="formResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 JavaScript功能测试</h3>
            <p>测试前端JavaScript功能是否正常</p>
            <button class="test-btn" onclick="testJavaScriptFunctions()">测试JS功能</button>
            <div id="jsResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 会话状态测试</h3>
            <p>测试JSP会话管理功能</p>
            <%
                // 测试会话功能
                String sessionId = session.getId();
                Date sessionCreated = new Date(session.getCreationTime());
                Date sessionAccessed = new Date(session.getLastAccessedTime());
                int sessionTimeout = session.getMaxInactiveInterval();
                
                // 设置一些会话属性用于测试
                if (session.getAttribute("visitCount") == null) {
                    session.setAttribute("visitCount", 1);
                } else {
                    int count = (Integer) session.getAttribute("visitCount");
                    session.setAttribute("visitCount", count + 1);
                }
            %>
            <div class="result">
                <p><strong>会话ID:</strong> <%= sessionId %></p>
                <p><strong>会话创建时间:</strong> <%= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(sessionCreated) %></p>
                <p><strong>最后访问时间:</strong> <%= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(sessionAccessed) %></p>
                <p><strong>会话超时时间:</strong> <%= sessionTimeout %> 秒</p>
                <p><strong>访问次数:</strong> <%= session.getAttribute("visitCount") %> 次</p>
                <p><strong>会话状态:</strong> <span class="status-success">活跃</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 请求参数测试</h3>
            <p>测试URL参数和请求头信息</p>
            <%
                // 显示请求参数
                Enumeration<String> paramNames = request.getParameterNames();
                Map<String, String[]> paramMap = request.getParameterMap();
            %>
            <div class="result">
                <h4>URL参数:</h4>
                <%
                if (paramMap.isEmpty()) {
                %>
                    <p>当前没有URL参数。可以尝试访问: <code>test.jsp?name=张三&dept=计算机学院</code></p>
                <%
                } else {
                    for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
                %>
                    <p><strong><%= entry.getKey() %>:</strong> <%= String.join(", ", entry.getValue()) %></p>
                <%
                    }
                }
                %>
                
                <h4>重要请求头:</h4>
                <p><strong>User-Agent:</strong> <%= request.getHeader("User-Agent") %></p>
                <p><strong>Accept:</strong> <%= request.getHeader("Accept") %></p>
                <p><strong>Accept-Language:</strong> <%= request.getHeader("Accept-Language") %></p>
                <p><strong>Referer:</strong> <%= request.getHeader("Referer") != null ? request.getHeader("Referer") : "无" %></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 导航链接</h3>
            <p>
                <a href="<%=request.getContextPath()%>/index.jsp" class="test-btn">返回主页</a>
                <a href="<%=request.getContextPath()%>/db-test.jsp" class="test-btn">数据库测试</a>
                <a href="<%=request.getContextPath()%>/java-error-check.jsp" class="test-btn">Java错误检查</a>
            </p>
        </div>
    </div>

    <script>
        // 设置全局上下文路径
        window.contextPath = '<%=request.getContextPath()%>';
        
        // 测试数据库连接
        function testDatabaseConnection() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试数据库连接...';
            
            // 发送AJAX请求到后端测试接口
            fetch(window.contextPath + '/api/test/database', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '<strong>✅ 数据库连接成功!</strong><br>' + 
                                        '连接信息: ' + (data.message || '连接正常');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '<strong>❌ 数据库连接失败!</strong><br>' + 
                                        '错误信息: ' + (data.message || '未知错误');
                }
            })
            .catch(error => {
                resultDiv.className = 'result warning';
                resultDiv.innerHTML = '<strong>⚠️ 无法连接到后端服务</strong><br>' + 
                                    '这可能是因为后端服务未启动或API接口未实现。<br>' +
                                    '错误: ' + error.message;
            });
        }
        
        // 测试API接口
        function testApiEndpoints() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在测试API接口...';
            
            const endpoints = [
                '/api/applicant/list',
                '/api/development/list',
                '/api/test/status'
            ];
            
            let results = [];
            let completed = 0;
            
            endpoints.forEach(endpoint => {
                fetch(window.contextPath + endpoint)
                .then(response => {
                    results.push({
                        endpoint: endpoint,
                        status: response.status,
                        success: response.ok
                    });
                })
                .catch(error => {
                    results.push({
                        endpoint: endpoint,
                        status: 'Error',
                        success: false,
                        error: error.message
                    });
                })
                .finally(() => {
                    completed++;
                    if (completed === endpoints.length) {
                        displayApiResults(results, resultDiv);
                    }
                });
            });
        }
        
        function displayApiResults(results, resultDiv) {
            let html = '<strong>API接口测试结果:</strong><br>';
            let allSuccess = true;
            
            results.forEach(result => {
                const icon = result.success ? '✅' : '❌';
                html += `${icon} ${result.endpoint}: ${result.status}<br>`;
                if (!result.success) allSuccess = false;
            });
            
            resultDiv.className = allSuccess ? 'result success' : 'result warning';
            resultDiv.innerHTML = html;
        }
        
        // 测试表单提交
        function testFormSubmission(event) {
            event.preventDefault();
            
            const resultDiv = document.getElementById('formResult');
            resultDiv.style.display = 'block';
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = '<strong>✅ 表单数据验证成功!</strong><br>' +
                                'Name: ' + data.name + '<br>' +
                                'Email: ' + data.email + '<br>' +
                                'Department: ' + data.department;
            
            return false;
        }
        
        // 测试JavaScript功能
        function testJavaScriptFunctions() {
            const resultDiv = document.getElementById('jsResult');
            resultDiv.style.display = 'block';
            
            let html = '<strong>JavaScript功能测试结果:</strong><br>';
            
            // 测试各种JS功能
            const tests = [
                { name: 'JSON支持', test: () => typeof JSON !== 'undefined' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                { name: 'LocalStorage', test: () => typeof localStorage !== 'undefined' },
                { name: 'Date对象', test: () => new Date().getTime() > 0 },
                { name: 'Array方法', test: () => [1,2,3].map(x => x*2).length === 3 }
            ];
            
            let allPassed = true;
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    html += `${passed ? '✅' : '❌'} ${test.name}: ${passed ? '支持' : '不支持'}<br>`;
                    if (!passed) allPassed = false;
                } catch (e) {
                    html += `❌ ${test.name}: 错误 - ${e.message}<br>`;
                    allPassed = false;
                }
            });
            
            resultDiv.className = allPassed ? 'result success' : 'result error';
            resultDiv.innerHTML = html;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JSP测试页面加载完成');
            console.log('上下文路径:', window.contextPath);
        });
    </script>
</body>
</html>
