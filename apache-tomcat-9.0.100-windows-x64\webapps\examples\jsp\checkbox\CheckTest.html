<HTML>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<HEAD>
<title>
checkbox.CheckTest Bean Properties
</title>
<BODY BGCOLOR="white">
<H2>
checkbox.CheckTest Bean Properties
</H2>
<HR>
<DL>
<DT>public class <B>CheckTest</B><DT>extends Object</DL>

<P>
<HR>

<P>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0">
<TR BGCOLOR="#EEEEFF">
<TD COLSPAN=3><FONT SIZE="+2">
<B>Properties Summary</B></FONT></TD>
</TR>
<TR BGCOLOR="white">
<td align="right" valign="top" width="1%">
<FONT SIZE="-1">
String
</FONT></TD>
<TD><B>CheckTest:fruit</B>
<BR>
       </TD>
<td width="1%">
<FONT SIZE="-1">
Multi
</FONT></TD>
</TABLE>
<HR>
</BODY>
</HTML>
