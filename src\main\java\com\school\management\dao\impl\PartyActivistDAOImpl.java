package com.school.management.dao.impl;

import com.school.management.dao.PartyActivistDAO;
import com.school.management.entity.PartyActivist;
import com.school.management.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 入党积极分子数据访问实现类
 */
public class PartyActivistDAOImpl implements PartyActivistDAO {
    
    @Override
    public Integer insert(PartyActivist activist) {
        String sql = "INSERT INTO party_activist (applicant_id, name, gender, birth_date, age, id_card, " +
                    "native_place, address, phone, grade, is_league_member, application_date, activist_date, " +
                    "branch_secretary, has_league_recommendation, status, remarks) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            stmt.setObject(1, activist.getApplicantId());
            stmt.setString(2, activist.getName());
            stmt.setString(3, activist.getGender());
            stmt.setDate(4, activist.getBirthDate() != null ? new java.sql.Date(activist.getBirthDate().getTime()) : null);
            stmt.setObject(5, activist.getAge());
            stmt.setString(6, activist.getIdCard());
            stmt.setString(7, activist.getNativePlace());
            stmt.setString(8, activist.getAddress());
            stmt.setString(9, activist.getPhone());
            stmt.setString(10, activist.getGrade());
            stmt.setObject(11, activist.getIsLeagueMember());
            stmt.setDate(12, activist.getApplicationDate() != null ? new java.sql.Date(activist.getApplicationDate().getTime()) : null);
            stmt.setDate(13, activist.getActivistDate() != null ? new java.sql.Date(activist.getActivistDate().getTime()) : null);
            stmt.setString(14, activist.getBranchSecretary());
            stmt.setObject(15, activist.getHasLeagueRecommendation());
            stmt.setString(16, activist.getStatus());
            stmt.setString(17, activist.getRemarks());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows > 0) {
                rs = stmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        } catch (SQLException e) {
            System.err.println("插入积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return null;
    }

    @Override
    public PartyActivist insertAndReturn(PartyActivist activist) {
        Integer id = insert(activist);
        if (id != null && id > 0) {
            activist.setId(id);
            return activist;
        }
        return null;
    }

    @Override
    public PartyActivist selectById(Integer id) {
        String sql = "SELECT * FROM party_activist WHERE id = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, id);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToActivist(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return null;
    }
    
    @Override
    public List<PartyActivist> selectAll() {
        String sql = "SELECT * FROM party_activist ORDER BY create_time DESC";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询所有积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    @Override
    public int update(PartyActivist activist) {
        String sql = "UPDATE party_activist SET name = ?, gender = ?, birth_date = ?, age = ?, " +
                    "native_place = ?, address = ?, phone = ?, grade = ?, is_league_member = ?, " +
                    "application_date = ?, activist_date = ?, branch_secretary = ?, " +
                    "has_league_recommendation = ?, status = ?, remarks = ? WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            
            stmt.setString(1, activist.getName());
            stmt.setString(2, activist.getGender());
            stmt.setDate(3, activist.getBirthDate() != null ? new java.sql.Date(activist.getBirthDate().getTime()) : null);
            stmt.setObject(4, activist.getAge());
            stmt.setString(5, activist.getNativePlace());
            stmt.setString(6, activist.getAddress());
            stmt.setString(7, activist.getPhone());
            stmt.setString(8, activist.getGrade());
            stmt.setObject(9, activist.getIsLeagueMember());
            stmt.setDate(10, activist.getApplicationDate() != null ? new java.sql.Date(activist.getApplicationDate().getTime()) : null);
            stmt.setDate(11, activist.getActivistDate() != null ? new java.sql.Date(activist.getActivistDate().getTime()) : null);
            stmt.setString(12, activist.getBranchSecretary());
            stmt.setObject(13, activist.getHasLeagueRecommendation());
            stmt.setString(14, activist.getStatus());
            stmt.setString(15, activist.getRemarks());
            stmt.setInt(16, activist.getId());
            
            int affectedRows = stmt.executeUpdate();
            return affectedRows;
        } catch (SQLException e) {
            System.err.println("更新积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, null);
        }
        return 0;
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM party_activist WHERE id = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, id);
            
            int affectedRows = stmt.executeUpdate();
            return affectedRows;
        } catch (SQLException e) {
            System.err.println("删除积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, null);
        }
        return 0;
    }

    @Override
    public int deleteByIds(Integer[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        StringBuilder sql = new StringBuilder("DELETE FROM party_activist WHERE id IN (");
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) sql.append(",");
            sql.append("?");
        }
        sql.append(")");

        Connection conn = null;
        PreparedStatement stmt = null;

        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql.toString());

            for (int i = 0; i < ids.length; i++) {
                stmt.setInt(i + 1, ids[i]);
            }

            return stmt.executeUpdate();
        } catch (SQLException e) {
            System.err.println("批量删除积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, null);
        }
        return 0;
    }

    @Override
    public boolean exists(Integer id) {
        return selectById(id) != null;
    }

    @Override
    public boolean existsByField(String fieldName, Object fieldValue) {
        String sql = "SELECT COUNT(*) FROM party_activist WHERE " + fieldName + " = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setObject(1, fieldValue);
            rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.err.println("检查字段值是否存在失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return false;
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM party_activist";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            System.err.println("统计积极分子数量失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return 0;
    }
    
    @Override
    public List<PartyActivist> selectByPage(int offset, int limit) {
        String sql = "SELECT * FROM party_activist ORDER BY create_time DESC LIMIT ? OFFSET ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, limit);
            stmt.setInt(2, offset);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("分页查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    @Override
    public List<PartyActivist> selectByConditions(Map<String, Object> conditions) {
        StringBuilder sql = new StringBuilder("SELECT * FROM party_activist WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, conditions);
        sql.append(" ORDER BY create_time DESC");
        
        return executeQuery(sql.toString(), params);
    }
    
    @Override
    public PartyActivist selectByApplicantId(Integer applicantId) {
        String sql = "SELECT * FROM party_activist WHERE applicant_id = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, applicantId);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToActivist(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据申请人ID查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return null;
    }
    
    @Override
    public PartyActivist selectByIdCard(String idCard) {
        String sql = "SELECT * FROM party_activist WHERE id_card = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, idCard);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return mapResultSetToActivist(rs);
            }
        } catch (SQLException e) {
            System.err.println("根据身份证号查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return null;
    }
    
    @Override
    public List<PartyActivist> selectByNameLike(String name) {
        String sql = "SELECT * FROM party_activist WHERE name LIKE ? ORDER BY create_time DESC";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, "%" + name + "%");
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据姓名模糊查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    @Override
    public List<PartyActivist> selectByGrade(String grade) {
        String sql = "SELECT * FROM party_activist WHERE grade = ? ORDER BY create_time DESC";
        return executeQueryWithSingleParam(sql, grade);
    }
    
    @Override
    public List<PartyActivist> selectByStatus(String status) {
        String sql = "SELECT * FROM party_activist WHERE status = ? ORDER BY create_time DESC";
        return executeQueryWithSingleParam(sql, status);
    }
    
    @Override
    public List<PartyActivist> selectByBranchSecretary(String branchSecretary) {
        String sql = "SELECT * FROM party_activist WHERE branch_secretary = ? ORDER BY create_time DESC";
        return executeQueryWithSingleParam(sql, branchSecretary);
    }
    
    @Override
    public List<PartyActivist> selectByLeagueRecommendation(Boolean hasLeagueRecommendation) {
        String sql = "SELECT * FROM party_activist WHERE has_league_recommendation = ? ORDER BY create_time DESC";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setBoolean(1, hasLeagueRecommendation);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据共青团推优查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    @Override
    public List<PartyActivist> selectByActivistDateRange(String startDate, String endDate) {
        String sql = "SELECT * FROM party_activist WHERE activist_date BETWEEN ? AND ? ORDER BY activist_date DESC";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, startDate);
            stmt.setString(2, endDate);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("根据日期范围查询积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    @Override
    public List<PartyActivist> selectByConditionsWithPage(Map<String, Object> conditions, int offset, int limit) {
        StringBuilder sql = new StringBuilder("SELECT * FROM party_activist WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, conditions);
        sql.append(" ORDER BY create_time DESC LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        
        return executeQuery(sql.toString(), params);
    }
    
    @Override
    public int countByConditions(Map<String, Object> conditions) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM party_activist WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        buildWhereClause(sql, params, conditions);
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql.toString());
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            System.err.println("根据条件统计积极分子数量失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return 0;
    }
    
    @Override
    public List<PartyActivist> selectByMultipleConditionsWithPage(String name, String grade, String status,
                                                                  String branchSecretary, Boolean hasLeagueRecommendation,
                                                                  int offset, int limit) {
        StringBuilder sql = new StringBuilder("SELECT * FROM party_activist WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (name != null && !name.trim().isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name.trim() + "%");
        }
        if (grade != null && !grade.trim().isEmpty()) {
            sql.append(" AND grade = ?");
            params.add(grade.trim());
        }
        if (status != null && !status.trim().isEmpty()) {
            sql.append(" AND status = ?");
            params.add(status.trim());
        }
        if (branchSecretary != null && !branchSecretary.trim().isEmpty()) {
            sql.append(" AND branch_secretary = ?");
            params.add(branchSecretary.trim());
        }
        if (hasLeagueRecommendation != null) {
            sql.append(" AND has_league_recommendation = ?");
            params.add(hasLeagueRecommendation);
        }
        
        sql.append(" ORDER BY create_time DESC LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        
        return executeQuery(sql.toString(), params);
    }
    
    @Override
    public int countByMultipleConditions(String name, String grade, String status,
                                        String branchSecretary, Boolean hasLeagueRecommendation) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM party_activist WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (name != null && !name.trim().isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name.trim() + "%");
        }
        if (grade != null && !grade.trim().isEmpty()) {
            sql.append(" AND grade = ?");
            params.add(grade.trim());
        }
        if (status != null && !status.trim().isEmpty()) {
            sql.append(" AND status = ?");
            params.add(status.trim());
        }
        if (branchSecretary != null && !branchSecretary.trim().isEmpty()) {
            sql.append(" AND branch_secretary = ?");
            params.add(branchSecretary.trim());
        }
        if (hasLeagueRecommendation != null) {
            sql.append(" AND has_league_recommendation = ?");
            params.add(hasLeagueRecommendation);
        }
        
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql.toString());
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            System.err.println("根据多条件统计积极分子数量失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return 0;
    }
    
    @Override
    public boolean existsByApplicantId(Integer applicantId) {
        String sql = "SELECT COUNT(*) FROM party_activist WHERE applicant_id = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, applicantId);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.err.println("检查申请人是否已是积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return false;
    }
    
    @Override
    public boolean existsByIdCard(String idCard) {
        String sql = "SELECT COUNT(*) FROM party_activist WHERE id_card = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setString(1, idCard);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            System.err.println("检查身份证号是否已存在失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return false;
    }
    
    @Override
    public boolean deleteByApplicantId(Integer applicantId) {
        String sql = "DELETE FROM party_activist WHERE applicant_id = ?";
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, applicantId);
            
            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            System.err.println("根据申请人ID删除积极分子失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, null);
        }
        return false;
    }
    
    /**
     * 将ResultSet映射为PartyActivist对象
     */
    private PartyActivist mapResultSetToActivist(ResultSet rs) throws SQLException {
        PartyActivist activist = new PartyActivist();
        activist.setId(rs.getInt("id"));
        activist.setApplicantId(rs.getObject("applicant_id", Integer.class));
        activist.setName(rs.getString("name"));
        activist.setGender(rs.getString("gender"));
        activist.setBirthDate(rs.getDate("birth_date"));
        activist.setAge(rs.getObject("age", Integer.class));
        activist.setIdCard(rs.getString("id_card"));
        activist.setNativePlace(rs.getString("native_place"));
        activist.setAddress(rs.getString("address"));
        activist.setPhone(rs.getString("phone"));
        activist.setGrade(rs.getString("grade"));
        activist.setIsLeagueMember(rs.getObject("is_league_member", Boolean.class));
        activist.setApplicationDate(rs.getDate("application_date"));
        activist.setActivistDate(rs.getDate("activist_date"));
        activist.setBranchSecretary(rs.getString("branch_secretary"));
        activist.setHasLeagueRecommendation(rs.getObject("has_league_recommendation", Boolean.class));
        activist.setStatus(rs.getString("status"));
        activist.setRemarks(rs.getString("remarks"));
        activist.setCreateTime(rs.getTimestamp("create_time"));
        activist.setUpdateTime(rs.getTimestamp("update_time"));
        return activist;
    }
    
    /**
     * 构建WHERE子句
     */
    private void buildWhereClause(StringBuilder sql, List<Object> params, Map<String, Object> conditions) {
        if (conditions != null) {
            for (Map.Entry<String, Object> entry : conditions.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value != null) {
                    if ("name".equals(key)) {
                        sql.append(" AND name LIKE ?");
                        params.add("%" + value + "%");
                    } else if ("grade".equals(key) || "status".equals(key) || "branchSecretary".equals(key)) {
                        sql.append(" AND ").append(key).append(" = ?");
                        params.add(value);
                    } else if ("hasLeagueRecommendation".equals(key)) {
                        sql.append(" AND has_league_recommendation = ?");
                        params.add(value);
                    } else if ("idCard".equals(key)) {
                        sql.append(" AND id_card = ?");
                        params.add(value);
                    }
                }
            }
        }
    }
    
    /**
     * 执行查询
     */
    private List<PartyActivist> executeQuery(String sql, List<Object> params) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            rs = stmt.executeQuery();
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("执行查询失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
    
    /**
     * 执行单参数查询
     */
    private List<PartyActivist> executeQueryWithSingleParam(String sql, Object param) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<PartyActivist> activists = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setObject(1, param);
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                activists.add(mapResultSetToActivist(rs));
            }
        } catch (SQLException e) {
            System.err.println("执行单参数查询失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeResources(conn, stmt, rs);
        }
        return activists;
    }
}
