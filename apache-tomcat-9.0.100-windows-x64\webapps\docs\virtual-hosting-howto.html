<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.100) - Virtual Hosting and Tomcat</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Virtual Hosting and Tomcat</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Assumptions">Assumptions</a></li><li><a href="#server.xml">server.xml</a></li><li><a href="#Webapps_Directory">Webapps Directory</a></li><li><a href="#Configuring_Your_Contexts">Configuring Your Contexts</a><ol><li><a href="#General">General</a></li><li><a href="#context.xml_-_approach__1">context.xml - approach #1</a></li><li><a href="#context.xml_-_approach__2">context.xml - approach #2</a></li><li><a href="#Defaults_per_host">Defaults per host</a></li><li><a href="#Further_Information">Further Information</a></li></ol></li></ul>
</div><h3 id="Assumptions">Assumptions</h3><div class="text">
    <p>
      For the sake of this how-to, assume you have a development host with two
      host names, <code>ren</code> and <code>stimpy</code>. Let's also assume
      one instance of Tomcat running, so <code>$CATALINA_HOME</code> refers to
      wherever it's installed, perhaps <code>/usr/local/tomcat</code>.
    </p>
    <p>
      Also, this how-to uses Unix-style path separators and commands; if you're
      on Windows modify accordingly.
    </p>
  </div><h3 id="server.xml">server.xml</h3><div class="text">
    <p>
      At the simplest, edit the <a href="config/engine.html">Engine</a> portion
      of your <code>server.xml</code> file to look like this:
    </p>
    <div class="codeBox"><pre><code>&lt;Engine name="Catalina" defaultHost="ren"&gt;
    &lt;Host name="ren"    appBase="renapps"/&gt;
    &lt;Host name="stimpy" appBase="stimpyapps"/&gt;
&lt;/Engine&gt;</code></pre></div>
    <p>
      Note that the directory structures under the appBase for each host should
      not overlap each other.
    </p>
    <p>
      Consult the configuration documentation for other attributes of the
      <a href="config/engine.html">Engine</a> and <a href="config/host.html">
      Host</a> elements.
    </p>
  </div><h3 id="Webapps_Directory">Webapps Directory</h3><div class="text">
    <p>
      Create directories for each of the virtual hosts:
    </p>
    <div class="codeBox"><pre><code>mkdir $CATALINA_HOME/renapps
mkdir $CATALINA_HOME/stimpyapps</code></pre></div>
  </div><h3 id="Configuring_Your_Contexts">Configuring Your Contexts</h3><div class="text">
    <div class="subsection"><h4 id="General">General</h4><div class="text">
      <p>Contexts are normally located underneath the appBase directory. For
       example, to deploy the <code>foobar</code> context as a war file in
       the <code>ren</code> host, use
       <code>$CATALINA_HOME/renapps/foobar.war</code>. Note that the
       default or ROOT context for <code>ren</code> would be deployed as
       <code>$CATALINA_HOME/renapps/ROOT.war</code> (WAR) or
       <code>$CATALINA_HOME/renapps/ROOT</code> (directory).
      </p>
      <p><strong>NOTE: The <code>docBase</code> for a context should never be
        the same as the <code>appBase</code> for a host.</strong>
      </p>
    </div></div>
    <div class="subsection"><h4 id="context.xml_-_approach__1">context.xml - approach #1</h4><div class="text">
      <p>
        Within your Context, create a <code>META-INF</code> directory and then
        place your Context definition in it in a file named
        <code>context.xml</code>. i.e.
        <code>$CATALINA_HOME/renapps/ROOT/META-INF/context.xml</code>
        This makes deployment easier, particularly if you're distributing a WAR
        file.
      </p>
    </div></div>
    <div class="subsection"><h4 id="context.xml_-_approach__2">context.xml - approach #2</h4><div class="text">
      <p>
        Create a structure under <code>$CATALINA_HOME/conf/Catalina</code>
        corresponding to your virtual hosts, e.g.:
      </p>
      <div class="codeBox"><pre><code>mkdir $CATALINA_HOME/conf/Catalina/ren
mkdir $CATALINA_HOME/conf/Catalina/stimpy</code></pre></div>
      <p>
        Note that the ending directory name "Catalina" represents the
        <code>name</code> attribute of the
        <a href="config/engine.html">Engine</a> element as shown above.
      </p>
      <p>
        Now, for your default webapps, add:
      </p>
      <div class="codeBox"><pre><code>$CATALINA_HOME/conf/Catalina/ren/ROOT.xml
$CATALINA_HOME/conf/Catalina/stimpy/ROOT.xml</code></pre></div>
      <p>
        If you want to use the Tomcat manager webapp for each host, you'll also
        need to add it here:
      </p>
      <div class="codeBox"><pre><code>cd $CATALINA_HOME/conf/Catalina
cp localhost/manager.xml ren/
cp localhost/manager.xml stimpy/</code></pre></div>
    </div></div>
    <div class="subsection"><h4 id="Defaults_per_host">Defaults per host</h4><div class="text">
      <p>
        You can override the default values found in <code>conf/context.xml</code>
        and <code>conf/web.xml</code> by specifying the new values in files
        named <code>context.xml.default</code> and <code>web.xml.default</code>
        from the host specific xml directory.</p>
      <p>Following our previous example, you could use
        <code>$CATALINA_HOME/conf/Catalina/ren/web.xml.default</code>
        to customize the defaults for all webapps that are deployed in the virtual
        host named <code>ren</code>.
      </p>
    </div></div>
    <div class="subsection"><h4 id="Further_Information">Further Information</h4><div class="text">
      <p>
        Consult the configuration documentation for other attributes of the
        <a href="config/context.html">Context</a> element.
      </p>
    </div></div>
  </div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>