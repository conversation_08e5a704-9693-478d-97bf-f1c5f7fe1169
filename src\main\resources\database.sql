-- 学校党员信息管理系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS management
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE management;

-- 1. 入党申请人表
CREATE TABLE party_applicant (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') COMMENT '性别（自动识别）',
    birth_date DATE COMMENT '出生日期（自动识别）',
    age INT COMMENT '年龄（自动计算）',
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    native_place VARCHAR(100) COMMENT '户籍地',
    address VARCHAR(200) COMMENT '地址',
    phone VARCHAR(20) COMMENT '联系电话',
    grade VARCHAR(20) COMMENT '年级',
    is_league_member BOOLEAN DEFAULT FALSE COMMENT '是否为共青团员',
    application_date DATE COMMENT '申请日期',
    status ENUM('待审核', '已通过', '已拒绝') DEFAULT '待审核' COMMENT '申请状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '入党申请人表';

-- 2. 入党积极分子表
CREATE TABLE party_activist (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    applicant_id INT COMMENT '申请人ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') COMMENT '性别（自动识别）',
    birth_date DATE COMMENT '出生日期（自动识别）',
    age INT COMMENT '年龄（自动计算）',
    id_card VARCHAR(18) COMMENT '身份证号',
    native_place VARCHAR(100) COMMENT '户籍地',
    address VARCHAR(200) COMMENT '地址',
    phone VARCHAR(20) COMMENT '联系电话',
    grade VARCHAR(20) COMMENT '年级',
    is_league_member BOOLEAN DEFAULT FALSE COMMENT '是否为共青团员',
    application_date DATE COMMENT '申请日期',
    activist_date DATE COMMENT '确定积极分子日期',
    branch_secretary VARCHAR(50) COMMENT '支部书记',
    has_league_recommendation BOOLEAN DEFAULT FALSE COMMENT '是否经过共青团推优',
    status VARCHAR(20) DEFAULT '积极分子' COMMENT '状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (applicant_id) REFERENCES party_applicant(id) ON DELETE SET NULL
) COMMENT '入党积极分子表';

-- 3. 发展对象表
CREATE TABLE development_target (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    activist_id INT COMMENT '积极分子ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    birth_date DATE NOT NULL COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    phone VARCHAR(11) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    department VARCHAR(100) NOT NULL COMMENT '所在院系',
    major VARCHAR(100) NOT NULL COMMENT '专业',
    class_name VARCHAR(50) NOT NULL COMMENT '班级',
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    determination_date DATE NOT NULL COMMENT '确定为发展对象日期',
    political_review_date DATE COMMENT '政治审查日期',
    political_review_result VARCHAR(20) COMMENT '政治审查结果',
    training_completion_date DATE COMMENT '培训完成日期',
    training_score DECIMAL(5,2) COMMENT '培训成绩',
    recommendation_letter TEXT COMMENT '推荐信',
    personal_statement TEXT COMMENT '个人陈述',
    status ENUM('政治审查中', '培训中', '准备入党', '已转预备党员', '已取消') DEFAULT '政治审查中' COMMENT '状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (activist_id) REFERENCES party_activist(id) ON DELETE SET NULL
) COMMENT '发展对象表';

-- 4. 预备党员表
CREATE TABLE probationary_member (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    development_id INT COMMENT '发展对象ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    birth_date DATE NOT NULL COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    phone VARCHAR(11) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    department VARCHAR(100) NOT NULL COMMENT '所在院系',
    major VARCHAR(100) NOT NULL COMMENT '专业',
    class_name VARCHAR(50) NOT NULL COMMENT '班级',
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    admission_date DATE NOT NULL COMMENT '入党日期',
    probation_start_date DATE NOT NULL COMMENT '预备期开始日期',
    probation_end_date DATE NOT NULL COMMENT '预备期结束日期',
    party_branch VARCHAR(100) COMMENT '所在党支部',
    introducer1_name VARCHAR(50) COMMENT '入党介绍人1',
    introducer2_name VARCHAR(50) COMMENT '入党介绍人2',
    quarterly_report1 TEXT COMMENT '第一季度思想汇报',
    quarterly_report2 TEXT COMMENT '第二季度思想汇报',
    quarterly_report3 TEXT COMMENT '第三季度思想汇报',
    quarterly_report4 TEXT COMMENT '第四季度思想汇报',
    performance_evaluation TEXT COMMENT '预备期表现评价',
    status ENUM('预备期中', '准备转正', '已转正式党员', '延长预备期', '取消预备党员资格') DEFAULT '预备期中' COMMENT '状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (development_id) REFERENCES development_target(id) ON DELETE SET NULL
) COMMENT '预备党员表';

-- 5. 正式党员表
CREATE TABLE formal_member (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    probationary_id INT COMMENT '预备党员ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    birth_date DATE NOT NULL COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    phone VARCHAR(11) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    department VARCHAR(100) NOT NULL COMMENT '所在院系',
    major VARCHAR(100) NOT NULL COMMENT '专业',
    class_name VARCHAR(50) NOT NULL COMMENT '班级',
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    admission_date DATE NOT NULL COMMENT '入党日期',
    formal_date DATE NOT NULL COMMENT '转正日期',
    party_branch VARCHAR(100) COMMENT '所在党支部',
    party_position VARCHAR(50) COMMENT '党内职务',
    annual_evaluation TEXT COMMENT '年度评议',
    honors_awards TEXT COMMENT '荣誉奖励',
    volunteer_activities TEXT COMMENT '志愿活动记录',
    continuing_education TEXT COMMENT '继续教育记录',
    status ENUM('正常', '停权', '开除党籍') DEFAULT '正常' COMMENT '党员状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (probationary_id) REFERENCES probationary_member(id) ON DELETE SET NULL
) COMMENT '正式党员表';

-- 6. 组织关系介绍信表
CREATE TABLE transfer_letter (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    member_id INT COMMENT '党员ID',
    member_name VARCHAR(50) NOT NULL COMMENT '党员姓名',
    id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
    original_branch VARCHAR(100) NOT NULL COMMENT '原党支部',
    target_branch VARCHAR(100) NOT NULL COMMENT '目标党支部',
    transfer_reason VARCHAR(200) NOT NULL COMMENT '转移原因',
    issue_date DATE NOT NULL COMMENT '开具日期',
    valid_until DATE NOT NULL COMMENT '有效期至',
    letter_number VARCHAR(50) UNIQUE NOT NULL COMMENT '介绍信编号',
    issuer_name VARCHAR(50) NOT NULL COMMENT '开具人姓名',
    issuer_position VARCHAR(50) COMMENT '开具人职务',
    contact_phone VARCHAR(11) COMMENT '联系电话',
    status ENUM('已开具', '已接收', '已过期', '已作废') DEFAULT '已开具' COMMENT '状态',
    receive_date DATE COMMENT '接收日期',
    receive_branch VARCHAR(100) COMMENT '接收党支部',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (member_id) REFERENCES formal_member(id) ON DELETE SET NULL
) COMMENT '组织关系介绍信表';

-- 创建索引以提高查询性能
CREATE INDEX idx_applicant_student_id ON party_applicant(student_id);
CREATE INDEX idx_applicant_name ON party_applicant(name);
CREATE INDEX idx_applicant_department ON party_applicant(department);

CREATE INDEX idx_activist_student_id ON party_activist(student_id);
CREATE INDEX idx_activist_name ON party_activist(name);
CREATE INDEX idx_activist_department ON party_activist(department);

CREATE INDEX idx_development_student_id ON development_target(student_id);
CREATE INDEX idx_development_name ON development_target(name);
CREATE INDEX idx_development_department ON development_target(department);

CREATE INDEX idx_probationary_student_id ON probationary_member(student_id);
CREATE INDEX idx_probationary_name ON probationary_member(name);
CREATE INDEX idx_probationary_department ON probationary_member(department);

CREATE INDEX idx_formal_student_id ON formal_member(student_id);
CREATE INDEX idx_formal_name ON formal_member(name);
CREATE INDEX idx_formal_department ON formal_member(department);

CREATE INDEX idx_transfer_letter_number ON transfer_letter(letter_number);
CREATE INDEX idx_transfer_member_name ON transfer_letter(member_name);
