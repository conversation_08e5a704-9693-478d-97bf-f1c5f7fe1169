package com.school.management.dao;

import java.util.List;
import java.util.Map;

/**
 * 基础DAO接口
 * 定义通用的数据访问操作
 * @param <T> 实体类型
 */
public interface BaseDAO<T> {
    
    /**
     * 插入一条记录
     * @param entity 实体对象
     * @return 插入成功返回生成的主键ID，失败返回null
     */
    Integer insert(T entity);
    
    /**
     * 根据ID删除记录
     * @param id 主键ID
     * @return 删除的记录数
     */
    int deleteById(Integer id);
    
    /**
     * 批量删除记录
     * @param ids 主键ID数组
     * @return 删除的记录数
     */
    int deleteByIds(Integer[] ids);
    
    /**
     * 更新记录
     * @param entity 实体对象
     * @return 更新的记录数
     */
    int update(T entity);
    
    /**
     * 根据ID查询记录
     * @param id 主键ID
     * @return 实体对象，不存在返回null
     */
    T selectById(Integer id);
    
    /**
     * 查询所有记录
     * @return 实体对象列表
     */
    List<T> selectAll();
    
    /**
     * 分页查询记录
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 实体对象列表
     */
    List<T> selectByPage(int offset, int limit);
    
    /**
     * 根据条件查询记录
     * @param conditions 查询条件Map
     * @return 实体对象列表
     */
    List<T> selectByConditions(Map<String, Object> conditions);
    
    /**
     * 根据条件分页查询记录
     * @param conditions 查询条件Map
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 实体对象列表
     */
    List<T> selectByConditionsWithPage(Map<String, Object> conditions, int offset, int limit);
    
    /**
     * 统计总记录数
     * @return 总记录数
     */
    int count();
    
    /**
     * 根据条件统计记录数
     * @param conditions 查询条件Map
     * @return 记录数
     */
    int countByConditions(Map<String, Object> conditions);
    
    /**
     * 检查记录是否存在
     * @param id 主键ID
     * @return 存在返回true，否则返回false
     */
    boolean exists(Integer id);
    
    /**
     * 根据字段值检查记录是否存在
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 存在返回true，否则返回false
     */
    boolean existsByField(String fieldName, Object fieldValue);
}
