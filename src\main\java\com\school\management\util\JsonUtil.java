package com.school.management.util;

// 注意：这里使用原生JSON处理，避免依赖外部库
// 如果需要使用Gson，请添加相应的jar包到项目中

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * JSON工具类
 * 提供简单的JSON序列化功能（原生实现）
 */
public class JsonUtil {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 将对象转换为JSON字符串（简单实现）
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return "null";
        }

        if (obj instanceof String) {
            return "\"" + escapeJson((String) obj) + "\"";
        }

        if (obj instanceof Number || obj instanceof Boolean) {
            return obj.toString();
        }

        if (obj instanceof Date) {
            return "\"" + dateFormat.format((Date) obj) + "\"";
        }

        if (obj instanceof Map) {
            return mapToJson((Map<?, ?>) obj);
        }

        if (obj instanceof List) {
            return listToJson((List<?>) obj);
        }

        // 对于其他对象，返回简单的字符串表示
        return "\"" + escapeJson(obj.toString()) + "\"";
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private static String escapeJson(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 将Map转换为JSON字符串
     */
    private static String mapToJson(Map<?, ?> map) {
        StringBuilder sb = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            if (!first) sb.append(",");
            sb.append("\"").append(entry.getKey()).append("\":");
            sb.append(toJson(entry.getValue()));
            first = false;
        }
        sb.append("}");
        return sb.toString();
    }

    /**
     * 将List转换为JSON字符串
     */
    private static String listToJson(List<?> list) {
        StringBuilder sb = new StringBuilder("[");
        boolean first = true;
        for (Object item : list) {
            if (!first) sb.append(",");
            sb.append(toJson(item));
            first = false;
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 简单的JSON解析（仅支持基本类型）
     * 注意：这是一个简化版本，建议在生产环境中使用专业的JSON库
     */
    @SuppressWarnings("unchecked")
    public static <T> T fromJson(String json, Class<T> clazz) {
        // 这里只是一个占位实现，实际项目中建议使用Gson或Jackson
        System.out.println("警告：使用简化版JSON解析，建议使用专业JSON库");
        return null;
    }
    
    /**
     * 创建成功响应的JSON
     * @param data 响应数据
     * @return JSON字符串
     */
    public static String createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "操作成功");
        response.put("data", data);
        return toJson(response);
    }
    
    /**
     * 创建成功响应的JSON（带自定义消息）
     * @param data 响应数据
     * @param message 自定义消息
     * @return JSON字符串
     */
    public static String createSuccessResponse(Object data, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("data", data);
        return toJson(response);
    }
    
    /**
     * 创建错误响应的JSON
     * @param message 错误消息
     * @return JSON字符串
     */
    public static String createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("data", null);
        return toJson(response);
    }
    
    /**
     * 向HTTP响应中写入JSON数据
     * @param response HTTP响应对象
     * @param json JSON字符串
     * @throws IOException IO异常
     */
    public static void writeJsonResponse(HttpServletResponse response, String json) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        PrintWriter out = response.getWriter();
        out.print(json);
        out.flush();
        out.close();
    }

    /**
     * 向HTTP响应中写入成功的JSON响应
     * @param response HTTP响应对象
     * @param data 响应数据
     * @throws IOException IO异常
     */
    public static void writeSuccessResponse(HttpServletResponse response, Object data) throws IOException {
        writeJsonResponse(response, createSuccessResponse(data));
    }

    /**
     * 向HTTP响应中写入成功的JSON响应（带自定义消息）
     * @param response HTTP响应对象
     * @param data 响应数据
     * @param message 自定义消息
     * @throws IOException IO异常
     */
    public static void writeSuccessResponse(HttpServletResponse response, Object data, String message) throws IOException {
        writeJsonResponse(response, createSuccessResponse(data, message));
    }

    /**
     * 向HTTP响应中写入错误的JSON响应
     * @param response HTTP响应对象
     * @param message 错误消息
     * @throws IOException IO异常
     */
    public static void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        writeJsonResponse(response, createErrorResponse(message));
    }
}
