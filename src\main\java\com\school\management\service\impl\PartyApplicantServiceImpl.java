package com.school.management.service.impl;

import com.school.management.dao.PartyApplicantDAO;
import com.school.management.dao.impl.PartyApplicantDAOImpl;
import com.school.management.entity.PartyApplicant;
import com.school.management.service.PartyApplicantService;
import com.school.management.service.BaseService;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 入党申请人Service实现类
 */
public class PartyApplicantServiceImpl implements PartyApplicantService {
    
    private PartyApplicantDAO dao = new PartyApplicantDAOImpl();
    
    // 身份证号正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    @Override
    public Integer add(PartyApplicant entity) {
        // 验证数据
        String validationError = validate(entity);
        if (validationError != null) {
            System.err.println("添加失败，数据验证错误: " + validationError);
            return null;
        }
        
        // 注意：新版本中没有学号字段，这个检查已移除
        
        // 检查身份证号是否已存在
        if (isIdCardExists(entity.getIdCard())) {
            System.err.println("添加失败，身份证号已存在: " + entity.getIdCard());
            return null;
        }
        
        // 设置默认值
        if (entity.getStatus() == null || entity.getStatus().trim().isEmpty()) {
            entity.setStatus("待审核");
        }
        // 注意：新版本中没有政治面貌字段，这个设置已移除
        
        return dao.insert(entity);
    }
    
    @Override
    public boolean deleteById(Integer id) {
        if (id == null || id <= 0) {
            return false;
        }
        return dao.deleteById(id) > 0;
    }
    
    @Override
    public int deleteByIds(Integer[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        return dao.deleteByIds(ids);
    }
    
    @Override
    public boolean update(PartyApplicant entity) {
        if (entity == null || entity.getId() == null) {
            return false;
        }
        
        // 验证数据
        String validationError = validate(entity);
        if (validationError != null) {
            System.err.println("更新失败，数据验证错误: " + validationError);
            return false;
        }
        
        // 注意：新版本中没有学号字段，这个检查已移除
        
        // 检查身份证号是否已存在（排除当前记录）
        if (isIdCardExists(entity.getIdCard(), entity.getId())) {
            System.err.println("更新失败，身份证号已存在: " + entity.getIdCard());
            return false;
        }
        
        return dao.update(entity) > 0;
    }
    
    @Override
    public PartyApplicant getById(Integer id) {
        if (id == null || id <= 0) {
            return null;
        }
        return dao.selectById(id);
    }
    
    @Override
    public List<PartyApplicant> getAll() {
        return dao.selectAll();
    }
    
    @Override
    public BaseService.PageResult<PartyApplicant> getByPage(int pageNum, int pageSize) {
        if (pageNum <= 0) pageNum = 1;
        if (pageSize <= 0) pageSize = 10;

        int offset = (pageNum - 1) * pageSize;
        List<PartyApplicant> data = dao.selectByPage(offset, pageSize);
        int total = dao.count();

        return new BaseService.PageResult<>(data, pageNum, pageSize, total);
    }
    
    @Override
    public List<PartyApplicant> getByConditions(Map<String, Object> conditions) {
        return dao.selectByConditions(conditions);
    }
    
    @Override
    public BaseService.PageResult<PartyApplicant> getByConditionsWithPage(Map<String, Object> conditions, int pageNum, int pageSize) {
        if (pageNum <= 0) pageNum = 1;
        if (pageSize <= 0) pageSize = 10;

        int offset = (pageNum - 1) * pageSize;
        List<PartyApplicant> data = dao.selectByConditionsWithPage(conditions, offset, pageSize);
        int total = dao.countByConditions(conditions);

        return new BaseService.PageResult<>(data, pageNum, pageSize, total);
    }
    
    @Override
    public int count() {
        return dao.count();
    }
    
    @Override
    public int countByConditions(Map<String, Object> conditions) {
        return dao.countByConditions(conditions);
    }
    
    @Override
    public boolean exists(Integer id) {
        return dao.exists(id);
    }
    
    @Override
    public boolean existsByField(String fieldName, Object fieldValue) {
        return dao.existsByField(fieldName, fieldValue);
    }
    
    @Override
    public String validate(PartyApplicant entity) {
        if (entity == null) {
            return "申请人信息不能为空";
        }
        
        // 必填字段验证
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            return "姓名不能为空";
        }
        if (entity.getName().length() > 50) {
            return "姓名长度不能超过50个字符";
        }
        
        if (entity.getGender() == null || entity.getGender().trim().isEmpty()) {
            return "性别不能为空";
        }
        if (!"男".equals(entity.getGender()) && !"女".equals(entity.getGender())) {
            return "性别只能是'男'或'女'";
        }
        
        if (entity.getBirthDate() == null) {
            return "出生日期不能为空";
        }
        
        if (entity.getIdCard() == null || entity.getIdCard().trim().isEmpty()) {
            return "身份证号不能为空";
        }
        if (!ID_CARD_PATTERN.matcher(entity.getIdCard()).matches()) {
            return "身份证号格式不正确";
        }
        
        if (entity.getGrade() == null || entity.getGrade().trim().isEmpty()) {
            return "年级不能为空";
        }
        if (entity.getGrade().length() > 20) {
            return "年级长度不能超过20个字符";
        }
        
        // 可选字段验证
        if (entity.getPhone() != null && !entity.getPhone().trim().isEmpty()) {
            if (!PHONE_PATTERN.matcher(entity.getPhone()).matches()) {
                return "手机号格式不正确";
            }
        }
        

        
        return null; // 验证通过
    }
    
    @Override
    public PartyApplicant getByStudentId(String studentId) {
        // 注意：新版本中没有学号字段，此方法已不适用
        System.out.println("警告：getByStudentId方法已过时，新版本中没有学号字段");
        return null;
    }
    
    @Override
    public PartyApplicant getByIdCard(String idCard) {
        if (idCard == null || idCard.trim().isEmpty()) {
            return null;
        }
        return dao.selectByIdCard(idCard);
    }
    
    @Override
    public List<PartyApplicant> getByNameLike(String name) {
        if (name == null || name.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return dao.selectByNameLike(name);
    }
    
    @Override
    public List<PartyApplicant> getByDepartment(String department) {
        // 注意：新版本中没有院系字段，此方法已不适用
        System.out.println("警告：getByDepartment方法已过时，新版本中没有院系字段");
        return new ArrayList<>();
    }

    @Override
    public List<PartyApplicant> getByMajor(String major) {
        // 注意：新版本中没有专业字段，此方法已不适用
        System.out.println("警告：getByMajor方法已过时，新版本中没有专业字段");
        return new ArrayList<>();
    }

    @Override
    public List<PartyApplicant> getByClass(String className) {
        // 注意：新版本中没有班级字段，此方法已不适用
        System.out.println("警告：getByClass方法已过时，新版本中没有班级字段");
        return new ArrayList<>();
    }
    
    @Override
    public List<PartyApplicant> getByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return dao.selectByStatus(status);
    }

    @Override
    public List<PartyApplicant> getByApplicationDateRange(String startDate, String endDate) {
        if (startDate == null || startDate.trim().isEmpty() ||
            endDate == null || endDate.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return dao.selectByApplicationDateRange(startDate, endDate);
    }



    @Override
    public List<PartyApplicant> getByMultipleConditions(String name, String department,
                                                       String major, String className, String status) {
        // 注意：新版本中没有department, major, className字段，只使用name和status
        System.out.println("警告：getByMultipleConditions方法参数已过时，只使用name和status");
        Map<String, Object> conditions = new HashMap<>();
        if (name != null && !name.trim().isEmpty()) {
            conditions.put("name", name);
        }
        if (status != null && !status.trim().isEmpty()) {
            conditions.put("status", status);
        }
        return getByConditions(conditions);
    }

    @Override
    public BaseService.PageResult<PartyApplicant> getByMultipleConditionsWithPage(String name, String department,
                                                                     String major, String className, String status,
                                                                     int pageNum, int pageSize) {
        // 注意：新版本中没有department, major, className字段，只使用name和status
        System.out.println("警告：getByMultipleConditionsWithPage方法参数已过时，只使用name和status");
        if (pageNum <= 0) pageNum = 1;
        if (pageSize <= 0) pageSize = 10;

        Map<String, Object> conditions = new HashMap<>();
        if (name != null && !name.trim().isEmpty()) {
            conditions.put("name", name);
        }
        if (status != null && !status.trim().isEmpty()) {
            conditions.put("status", status);
        }

        return getByConditionsWithPage(conditions, pageNum, pageSize);
    }

    @Override
    public List<String> getAllDepartments() {
        // 注意：新版本中没有院系字段，此方法已不适用
        System.out.println("警告：getAllDepartments方法已过时，新版本中没有院系字段");
        return new ArrayList<>();
    }

    @Override
    public List<String> getMajorsByDepartment(String department) {
        // 注意：新版本中没有专业字段，此方法已不适用
        System.out.println("警告：getMajorsByDepartment方法已过时，新版本中没有专业字段");
        return new ArrayList<>();
    }

    @Override
    public List<String> getClassesByMajor(String major) {
        // 注意：新版本中没有班级字段，此方法已不适用
        System.out.println("警告：getClassesByMajor方法已过时，新版本中没有班级字段");
        return new ArrayList<>();
    }

    @Override
    public Map<String, Integer> getCountByStatus() {
        return dao.countByStatus();
    }

    @Override
    public boolean isStudentIdExists(String studentId) {
        // 注意：新版本中没有学号字段，此方法已不适用
        System.out.println("警告：isStudentIdExists方法已过时，新版本中没有学号字段");
        return false;
    }

    @Override
    public boolean isIdCardExists(String idCard) {
        if (idCard == null || idCard.trim().isEmpty()) {
            return false;
        }
        return dao.selectByIdCard(idCard) != null;
    }

    @Override
    public boolean isStudentIdExists(String studentId, Integer excludeId) {
        // 注意：新版本中没有学号字段，此方法已不适用
        System.out.println("警告：isStudentIdExists方法已过时，新版本中没有学号字段");
        return false;
    }

    @Override
    public boolean isIdCardExists(String idCard, Integer excludeId) {
        if (idCard == null || idCard.trim().isEmpty()) {
            return false;
        }
        PartyApplicant existing = dao.selectByIdCard(idCard);
        return existing != null && !existing.getId().equals(excludeId);
    }

    @Override
    public boolean approve(Integer id, String status, String remarks) {
        if (id == null || id <= 0) {
            return false;
        }

        PartyApplicant applicant = dao.selectById(id);
        if (applicant == null) {
            return false;
        }

        applicant.setStatus(status);
        applicant.setRemarks(remarks);

        return dao.update(applicant) > 0;
    }

    @Override
    public int batchApprove(Integer[] ids, String status, String remarks) {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        int successCount = 0;
        for (Integer id : ids) {
            if (approve(id, status, remarks)) {
                successCount++;
            }
        }
        return successCount;
    }

    @Override
    public List<PartyApplicant> exportData(Map<String, Object> conditions) {
        return dao.selectByConditions(conditions);
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总数统计
        statistics.put("total", dao.count());

        // 状态统计
        Map<String, Integer> statusCount = dao.countByStatus();
        statistics.put("statusCount", statusCount);

        // 院系统计
        List<String> departments = dao.selectAllDepartments();
        Map<String, Integer> departmentCount = new HashMap<>();
        for (String dept : departments) {
            departmentCount.put(dept, dao.selectByDepartment(dept).size());
        }
        statistics.put("departmentCount", departmentCount);

        return statistics;
    }
}
