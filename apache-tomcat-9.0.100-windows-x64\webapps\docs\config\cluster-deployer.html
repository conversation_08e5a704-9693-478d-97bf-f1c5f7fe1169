<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 Configuration Reference (9.0.100) - The Cluster Deployer object</title><meta name="author" content="Filip Hanik"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9 Configuration Reference</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">JASPIC</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Cluster Deployer object</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#org.apache.catalina.ha.deploy.FarmWarDeployer">org.apache.catalina.ha.deploy.FarmWarDeployer</a><ol><li><a href="#Attributes">Attributes</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>The Farm War Deployer can deploy and undeploy web applications on the other
  nodes in the cluster.</p>
  <p><strong>Note:</strong> FarmWarDeployer can be configured at host level
  cluster only.
  </p>
</div><h3 id="org.apache.catalina.ha.deploy.FarmWarDeployer">org.apache.catalina.ha.deploy.FarmWarDeployer</h3><div class="text">

  <div class="subsection"><h4 id="Attributes">Attributes</h4><div class="text">

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_className"><td><strong><code class="attributeName">className</code></strong></td><td>
        The cluster deployer class, currently only one is available,
        <code>org.apache.catalina.ha.deploy.FarmWarDeployer.</code>
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_deployDir"><td><strong><code class="attributeName">deployDir</code></strong></td><td>
        Deployment directory. This is the pathname of a directory where deploy
        the web applications. You may specify an absolute pathname, or a
        pathname that is relative to the $CATALINA_BASE directory. In the
        current implementation, this attribute must be the same value as the
        <strong>Host's appBase</strong>.
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_tempDir"><td><strong><code class="attributeName">tempDir</code></strong></td><td>
        The temporaryDirectory to store binary data when downloading a war from
        the cluster. You may specify an absolute pathname, or a pathname that is
        relative to the $CATALINA_BASE directory.
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_watchDir"><td><code class="attributeName">watchDir</code></td><td>
        This is the pathname of a directory where watch for changes(add/modify/remove)
        of web applications. You may specify an absolute pathname, or a pathname
        that is relative to the $CATALINA_BASE directory.
        <strong>Note: </strong> if <strong>watchEnabled</strong> is false, this
        attribute will have no effect.
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_watchEnabled"><td><code class="attributeName">watchEnabled</code></td><td>
        Set to true if you want to watch for changes of web applications.
        Only when this attribute set to true, you can trigger a deploy/undeploy
        of web applications. The flag's value defaults to false.
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_processDeployFrequency"><td><code class="attributeName">processDeployFrequency</code></td><td>
        Frequency of the Farm watchDir check. Cluster wide deployment will be
        done once for the specified amount of backgroundProcess calls (ie, the
        lower the amount, the most often the checks will occur). The minimum
        value is 1, and the default value is 2.
        <strong>Note: </strong> if <strong>watchEnabled</strong> is false, this
        attribute will have no effect.
      </td></tr><tr id="org.apache.catalina.ha.deploy.FarmWarDeployer_Attributes_maxValidTime"><td><code class="attributeName">maxValidTime</code></td><td>
        FileMessageFactory instances used by the FarmWarDeployer are only
        retained while they are required. When receiving a WAR file, the
        associated FileMessageFactory instance is deleted once the WAR file has
        been fully received. To avoid memory leaks under various error
        conditions (part of the file never received, very slow message transfer,
        etc.), this attribute defines the maximum time permitted between
        receiving valid messages that contain part of the WAR file. If that
        maximum time is exceeded, the FileMessageFactory will be deleted and the
        WAR file transfer will fail for that node. If a negative value is
        specified, the FileMessageFactory will only be removed once the WAR file
        is fully received. If not specified, the default value of 300 (5
        minutes) will be used.
      </td></tr></table>

  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>