package com.school.management.dao.impl;

import com.school.management.dao.PartyApplicantDAO;
import com.school.management.entity.PartyApplicant;
import com.school.management.util.DBUtil;

import java.sql.*;
import java.util.*;

/**
 * 入党申请人DAO实现类
 */
public class PartyApplicantDAOImpl implements PartyApplicantDAO {
    
    @Override
    public Integer insert(PartyApplicant entity) {
        String sql = "INSERT INTO party_applicant (name, gender, birth_date, age, id_card, " +
                    "native_place, address, phone, grade, is_league_member, application_date, status, remarks) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            pstmt.setString(1, entity.getName());
            pstmt.setString(2, entity.getGender());
            pstmt.setDate(3, entity.getBirthDate() != null ? new java.sql.Date(entity.getBirthDate().getTime()) : null);
            pstmt.setObject(4, entity.getAge());
            pstmt.setString(5, entity.getIdCard());
            pstmt.setString(6, entity.getNativePlace());
            pstmt.setString(7, entity.getAddress());
            pstmt.setString(8, entity.getPhone());
            pstmt.setString(9, entity.getGrade());
            pstmt.setObject(10, entity.getIsLeagueMember());
            pstmt.setDate(11, entity.getApplicationDate() != null ? new java.sql.Date(entity.getApplicationDate().getTime()) : null);
            pstmt.setString(12, entity.getStatus());
            pstmt.setString(13, entity.getRemarks());
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("插入入党申请人失败: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM party_applicant WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            System.err.println("删除入党申请人失败: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public int deleteByIds(Integer[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        
        StringBuilder sql = new StringBuilder("DELETE FROM party_applicant WHERE id IN (");
        for (int i = 0; i < ids.length; i++) {
            sql.append("?");
            if (i < ids.length - 1) {
                sql.append(",");
            }
        }
        sql.append(")");
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {
            
            for (int i = 0; i < ids.length; i++) {
                pstmt.setInt(i + 1, ids[i]);
            }
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            System.err.println("批量删除入党申请人失败: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public int update(PartyApplicant entity) {
        String sql = "UPDATE party_applicant SET name=?, gender=?, birth_date=?, age=?, id_card=?, " +
                    "native_place=?, address=?, phone=?, grade=?, is_league_member=?, application_date=?, " +
                    "status=?, remarks=? WHERE id=?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, entity.getName());
            pstmt.setString(2, entity.getGender());
            pstmt.setDate(3, entity.getBirthDate() != null ? new java.sql.Date(entity.getBirthDate().getTime()) : null);
            pstmt.setObject(4, entity.getAge());
            pstmt.setString(5, entity.getIdCard());
            pstmt.setString(6, entity.getNativePlace());
            pstmt.setString(7, entity.getAddress());
            pstmt.setString(8, entity.getPhone());
            pstmt.setString(9, entity.getGrade());
            pstmt.setObject(10, entity.getIsLeagueMember());
            pstmt.setDate(11, entity.getApplicationDate() != null ? new java.sql.Date(entity.getApplicationDate().getTime()) : null);
            pstmt.setString(12, entity.getStatus());
            pstmt.setString(13, entity.getRemarks());
            pstmt.setInt(14, entity.getId());
            
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            System.err.println("更新入党申请人失败: " + e.getMessage());
            e.printStackTrace();
            return 0;
        }
    }
    
    @Override
    public PartyApplicant selectById(Integer id) {
        String sql = "SELECT * FROM party_applicant WHERE id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, id);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToEntity(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("根据ID查询入党申请人失败: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    @Override
    public List<PartyApplicant> selectAll() {
        String sql = "SELECT * FROM party_applicant ORDER BY create_time DESC";
        List<PartyApplicant> list = new ArrayList<>();
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                list.add(mapResultSetToEntity(rs));
            }
        } catch (SQLException e) {
            System.err.println("查询所有入党申请人失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }
    
    @Override
    public List<PartyApplicant> selectByPage(int offset, int limit) {
        String sql = "SELECT * FROM party_applicant ORDER BY create_time DESC LIMIT ?, ?";
        List<PartyApplicant> list = new ArrayList<>();
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, offset);
            pstmt.setInt(2, limit);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("分页查询入党申请人失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }
    
    @Override
    public PartyApplicant selectByStudentId(String studentId) {
        // 注意：新版本中没有学号字段，此方法已不适用
        System.out.println("警告：selectByStudentId方法已过时，新版本中没有学号字段");
        return null;
    }
    
    @Override
    public PartyApplicant selectByIdCard(String idCard) {
        String sql = "SELECT * FROM party_applicant WHERE id_card = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, idCard);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToEntity(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("根据身份证号查询入党申请人失败: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 将ResultSet映射为实体对象
     */
    private PartyApplicant mapResultSetToEntity(ResultSet rs) throws SQLException {
        PartyApplicant entity = new PartyApplicant();
        entity.setId(rs.getInt("id"));
        entity.setName(rs.getString("name"));
        entity.setGender(rs.getString("gender"));
        entity.setBirthDate(rs.getDate("birth_date"));
        entity.setAge(rs.getObject("age", Integer.class));
        entity.setIdCard(rs.getString("id_card"));
        entity.setNativePlace(rs.getString("native_place"));
        entity.setAddress(rs.getString("address"));
        entity.setPhone(rs.getString("phone"));
        entity.setGrade(rs.getString("grade"));
        entity.setIsLeagueMember(rs.getObject("is_league_member", Boolean.class));
        entity.setApplicationDate(rs.getDate("application_date"));
        entity.setStatus(rs.getString("status"));
        entity.setRemarks(rs.getString("remarks"));
        entity.setCreateTime(rs.getTimestamp("create_time"));
        entity.setUpdateTime(rs.getTimestamp("update_time"));
        return entity;
    }
    
    // 其他方法的实现将在下一部分继续...
    @Override
    public List<PartyApplicant> selectByConditions(Map<String, Object> conditions) {
        // 简化实现，实际项目中需要动态构建SQL
        return selectAll();
    }
    
    @Override
    public List<PartyApplicant> selectByConditionsWithPage(Map<String, Object> conditions, int offset, int limit) {
        // 简化实现
        return selectByPage(offset, limit);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM party_applicant";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            System.err.println("统计入党申请人数量失败: " + e.getMessage());
            e.printStackTrace();
        }
        return 0;
    }
    
    @Override
    public int countByConditions(Map<String, Object> conditions) {
        // 简化实现
        return count();
    }
    
    @Override
    public boolean exists(Integer id) {
        return selectById(id) != null;
    }
    
    @Override
    public boolean existsByField(String fieldName, Object fieldValue) {
        String sql = "SELECT COUNT(*) FROM party_applicant WHERE " + fieldName + " = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setObject(1, fieldValue);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("检查字段值是否存在失败: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public List<PartyApplicant> selectByNameLike(String name) {
        String sql = "SELECT * FROM party_applicant WHERE name LIKE ? ORDER BY create_time DESC";
        List<PartyApplicant> list = new ArrayList<>();

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, "%" + name + "%");
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("根据姓名模糊查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public List<PartyApplicant> selectByDepartment(String department) {
        // 注意：新版本中没有院系字段，此方法已不适用
        System.out.println("警告：selectByDepartment方法已过时，新版本中没有院系字段");
        return new ArrayList<>();
    }

    @Override
    public List<PartyApplicant> selectByMajor(String major) {
        // 注意：新版本中没有专业字段，此方法已不适用
        System.out.println("警告：selectByMajor方法已过时，新版本中没有专业字段");
        return new ArrayList<>();
    }

    @Override
    public List<PartyApplicant> selectByClass(String className) {
        // 注意：新版本中没有班级字段，此方法已不适用
        System.out.println("警告：selectByClass方法已过时，新版本中没有班级字段");
        return new ArrayList<>();
    }

    @Override
    public List<PartyApplicant> selectByStatus(String status) {
        String sql = "SELECT * FROM party_applicant WHERE status = ? ORDER BY create_time DESC";
        List<PartyApplicant> list = new ArrayList<>();

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("根据状态查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public List<PartyApplicant> selectByApplicationDateRange(String startDate, String endDate) {
        String sql = "SELECT * FROM party_applicant WHERE application_date BETWEEN ? AND ? ORDER BY create_time DESC";
        List<PartyApplicant> list = new ArrayList<>();

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, startDate);
            pstmt.setString(2, endDate);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("根据申请日期范围查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public List<PartyApplicant> selectByMultipleConditions(String name, String department,
                                                         String major, String className, String status) {
        StringBuilder sql = new StringBuilder("SELECT * FROM party_applicant WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (name != null && !name.trim().isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name + "%");
        }
        if (department != null && !department.trim().isEmpty()) {
            sql.append(" AND department = ?");
            params.add(department);
        }
        if (major != null && !major.trim().isEmpty()) {
            sql.append(" AND major = ?");
            params.add(major);
        }
        if (className != null && !className.trim().isEmpty()) {
            sql.append(" AND class_name = ?");
            params.add(className);
        }
        if (status != null && !status.trim().isEmpty()) {
            sql.append(" AND status = ?");
            params.add(status);
        }
        sql.append(" ORDER BY create_time DESC");

        List<PartyApplicant> list = new ArrayList<>();
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("多条件查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public List<PartyApplicant> selectByMultipleConditionsWithPage(String name, String department,
                                                                 String major, String className, String status,
                                                                 int offset, int limit) {
        StringBuilder sql = new StringBuilder("SELECT * FROM party_applicant WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (name != null && !name.trim().isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name + "%");
        }
        if (department != null && !department.trim().isEmpty()) {
            sql.append(" AND department = ?");
            params.add(department);
        }
        if (major != null && !major.trim().isEmpty()) {
            sql.append(" AND major = ?");
            params.add(major);
        }
        if (className != null && !className.trim().isEmpty()) {
            sql.append(" AND class_name = ?");
            params.add(className);
        }
        if (status != null && !status.trim().isEmpty()) {
            sql.append(" AND status = ?");
            params.add(status);
        }
        sql.append(" ORDER BY create_time DESC LIMIT ?, ?");
        params.add(offset);
        params.add(limit);

        List<PartyApplicant> list = new ArrayList<>();
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    list.add(mapResultSetToEntity(rs));
                }
            }
        } catch (SQLException e) {
            System.err.println("多条件分页查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public int countByMultipleConditions(String name, String department,
                                       String major, String className, String status) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM party_applicant WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (name != null && !name.trim().isEmpty()) {
            sql.append(" AND name LIKE ?");
            params.add("%" + name + "%");
        }
        if (department != null && !department.trim().isEmpty()) {
            sql.append(" AND department = ?");
            params.add(department);
        }
        if (major != null && !major.trim().isEmpty()) {
            sql.append(" AND major = ?");
            params.add(major);
        }
        if (className != null && !className.trim().isEmpty()) {
            sql.append(" AND class_name = ?");
            params.add(className);
        }
        if (status != null && !status.trim().isEmpty()) {
            sql.append(" AND status = ?");
            params.add(status);
        }

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                pstmt.setObject(i + 1, params.get(i));
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        } catch (SQLException e) {
            System.err.println("多条件统计失败: " + e.getMessage());
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public List<String> selectAllDepartments() {
        // 注意：新版本中没有院系字段，此方法已不适用
        System.out.println("警告：selectAllDepartments方法已过时，新版本中没有院系字段");
        return new ArrayList<>();
    }

    @Override
    public List<String> selectMajorsByDepartment(String department) {
        // 注意：新版本中没有专业字段，此方法已不适用
        System.out.println("警告：selectMajorsByDepartment方法已过时，新版本中没有专业字段");
        return new ArrayList<>();
    }

    @Override
    public List<String> selectClassesByMajor(String major) {
        // 注意：新版本中没有班级字段，此方法已不适用
        System.out.println("警告：selectClassesByMajor方法已过时，新版本中没有班级字段");
        return new ArrayList<>();
    }

    @Override
    public Map<String, Integer> countByStatus() {
        String sql = "SELECT status, COUNT(*) as count FROM party_applicant GROUP BY status";
        Map<String, Integer> map = new HashMap<>();

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                map.put(rs.getString("status"), rs.getInt("count"));
            }
        } catch (SQLException e) {
            System.err.println("统计各状态数量失败: " + e.getMessage());
            e.printStackTrace();
        }
        return map;
    }
}
