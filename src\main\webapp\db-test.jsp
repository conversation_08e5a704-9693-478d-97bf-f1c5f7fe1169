<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.sql.*" %>
<%@ page import="com.school.management.util.DBUtil" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试 - 学校党员信息管理系统</title>
    <link rel="stylesheet" href="<%=request.getContextPath()%>/css/style.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child { border-bottom: none; }
        .config-label { font-weight: bold; color: #555; }
        .config-value {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .test-btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .table-info {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .table-info th, .table-info td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .table-info th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🗄️ 数据库连接测试</h1>
        
        <div class="config-section">
            <h3>📋 当前数据库配置</h3>
            <div class="config-item">
                <span class="config-label">数据库类型:</span>
                <span class="config-value">MySQL 8.0+</span>
            </div>
            <div class="config-item">
                <span class="config-label">连接地址:</span>
                <span class="config-value">**************************************</span>
            </div>
            <div class="config-item">
                <span class="config-label">数据库名:</span>
                <span class="config-value">management</span>
            </div>
            <div class="config-item">
                <span class="config-label">用户名:</span>
                <span class="config-value">root</span>
            </div>
            <div class="config-item">
                <span class="config-label">字符编码:</span>
                <span class="config-value">UTF-8</span>
            </div>
            <div class="config-item">
                <span class="config-label">时区:</span>
                <span class="config-value">Asia/Shanghai</span>
            </div>
            <div class="config-item">
                <span class="config-label">测试时间:</span>
                <span class="config-value"><%= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) %></span>
            </div>
        </div>

        <div class="config-section">
            <h3>🧪 实时数据库连接测试</h3>
            <%
                boolean connectionSuccess = false;
                String connectionMessage = "";
                String databaseInfo = "";
                List<String> tableList = new ArrayList<>();
                
                try {
                    // 尝试获取数据库连接
                    Connection conn = DBUtil.getConnection();
                    
                    if (conn != null && !conn.isClosed()) {
                        connectionSuccess = true;
                        connectionMessage = "数据库连接成功！";
                        
                        // 获取数据库信息
                        DatabaseMetaData metaData = conn.getMetaData();
                        databaseInfo = "数据库: " + metaData.getDatabaseProductName() + " " + 
                                     metaData.getDatabaseProductVersion() + "<br>" +
                                     "驱动: " + metaData.getDriverName() + " " + 
                                     metaData.getDriverVersion() + "<br>" +
                                     "URL: " + metaData.getURL() + "<br>" +
                                     "用户: " + metaData.getUserName();
                        
                        // 获取表列表
                        ResultSet tables = metaData.getTables(conn.getCatalog(), null, "%", new String[]{"TABLE"});
                        while (tables.next()) {
                            tableList.add(tables.getString("TABLE_NAME"));
                        }
                        tables.close();
                        
                        conn.close();
                    } else {
                        connectionMessage = "数据库连接失败：连接为空或已关闭";
                    }
                    
                } catch (Exception e) {
                    connectionMessage = "数据库连接异常: " + e.getMessage();
                }
            %>
            
            <div class="result <%= connectionSuccess ? "success" : "error" %>">
                <h4>连接测试结果</h4>
                <p><strong>状态:</strong> <%= connectionSuccess ? "✅ 成功" : "❌ 失败" %></p>
                <p><strong>消息:</strong> <%= connectionMessage %></p>
                
                <% if (connectionSuccess) { %>
                    <h4>数据库信息</h4>
                    <p><%= databaseInfo %></p>
                    
                    <h4>数据表列表 (<%= tableList.size() %> 个表)</h4>
                    <% if (tableList.isEmpty()) { %>
                        <p class="warning">⚠️ 未找到任何数据表，请执行数据库初始化脚本</p>
                    <% } else { %>
                        <table class="table-info">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>表名</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <%
                                    String[] tableDescriptions = {
                                        "入党申请人信息表",
                                        "入党积极分子信息表", 
                                        "发展对象信息表",
                                        "预备党员信息表",
                                        "正式党员信息表",
                                        "组织关系介绍信表"
                                    };
                                    
                                    for (int i = 0; i < tableList.size(); i++) {
                                        String tableName = tableList.get(i);
                                        String description = i < tableDescriptions.length ? 
                                                           tableDescriptions[i] : "数据表";
                                %>
                                <tr>
                                    <td><%= i + 1 %></td>
                                    <td><code><%= tableName %></code></td>
                                    <td><%= description %></td>
                                </tr>
                                <% } %>
                            </tbody>
                        </table>
                    <% } %>
                <% } %>
            </div>
        </div>

        <div class="config-section">
            <h3>📊 数据表记录统计</h3>
            <%
                Map<String, Integer> tableCounts = new HashMap<>();
                if (connectionSuccess) {
                    try {
                        Connection conn = DBUtil.getConnection();
                        for (String tableName : tableList) {
                            try {
                                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM " + tableName);
                                ResultSet rs = stmt.executeQuery();
                                if (rs.next()) {
                                    tableCounts.put(tableName, rs.getInt(1));
                                }
                                rs.close();
                                stmt.close();
                            } catch (SQLException e) {
                                tableCounts.put(tableName, -1); // 表示查询失败
                            }
                        }
                        conn.close();
                    } catch (Exception e) {
                        // 忽略统计错误
                    }
                }
            %>
            
            <% if (connectionSuccess && !tableList.isEmpty()) { %>
                <table class="table-info">
                    <thead>
                        <tr>
                            <th>表名</th>
                            <th>记录数</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (String tableName : tableList) { %>
                        <tr>
                            <td><code><%= tableName %></code></td>
                            <td>
                                <% 
                                    Integer count = tableCounts.get(tableName);
                                    if (count != null && count >= 0) {
                                        out.print(count + " 条");
                                    } else {
                                        out.print("查询失败");
                                    }
                                %>
                            </td>
                            <td>
                                <% 
                                    Integer count = tableCounts.get(tableName);
                                    if (count != null && count >= 0) {
                                        out.print("<span class='status-success'>正常</span>");
                                    } else {
                                        out.print("<span class='status-error'>异常</span>");
                                    }
                                %>
                            </td>
                        </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="result warning">
                    <p>⚠️ 无法获取表统计信息，请确保数据库连接正常且表已创建</p>
                </div>
            <% } %>
        </div>

        <div class="config-section">
            <h3>🚀 数据库部署状态</h3>
            <div class="result">
                <h4>部署检查清单:</h4>
                <ul>
                    <li>✅ MySQL服务: <%= connectionSuccess ? "已启动" : "❌ 未启动或连接失败" %></li>
                    <li>✅ 数据库management: <%= connectionSuccess ? "已创建" : "❌ 未创建或无法访问" %></li>
                    <li>✅ 数据表: <%= tableList.size() %> 个表 <%= tableList.size() >= 6 ? "(完整)" : "(不完整，预期6个表)" %></li>
                    <li>✅ 字符编码: UTF-8</li>
                    <li>✅ 连接池: 已配置</li>
                </ul>
                
                <% if (!connectionSuccess || tableList.size() < 6) { %>
                <h4>建议操作:</h4>
                <ol>
                    <li>确保MySQL服务已启动</li>
                    <li>创建数据库: <code>CREATE DATABASE management CHARACTER SET utf8mb4;</code></li>
                    <li>执行建表脚本: <code>mysql -u root -p management < database.sql</code></li>
                    <li>检查用户权限和密码</li>
                </ol>
                <% } %>
            </div>
        </div>

        <div class="config-section">
            <h3>🔗 相关链接</h3>
            <p>
                <a href="<%=request.getContextPath()%>/index.jsp" class="test-btn">返回主页</a>
                <a href="<%=request.getContextPath()%>/test.jsp" class="test-btn">功能测试</a>
                <a href="<%=request.getContextPath()%>/java-error-check.jsp" class="test-btn">Java错误检查</a>
                <button class="test-btn" onclick="location.reload()">刷新测试</button>
            </p>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据库测试页面加载完成');
            console.log('连接状态: <%= connectionSuccess %>');
            console.log('表数量: <%= tableList.size() %>');
            
            // 如果连接成功，显示成功消息
            <% if (connectionSuccess) { %>
                console.log('✅ 数据库连接正常');
            <% } else { %>
                console.log('❌ 数据库连接失败');
            <% } %>
        });
        
        // 自动刷新功能（可选）
        function autoRefresh() {
            if (confirm('是否要启用自动刷新（每30秒）？')) {
                setInterval(function() {
                    location.reload();
                }, 30000);
                alert('已启用自动刷新，每30秒刷新一次');
            }
        }
    </script>
</body>
</html>
