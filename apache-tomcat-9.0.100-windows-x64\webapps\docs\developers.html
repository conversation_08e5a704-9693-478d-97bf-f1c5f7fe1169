<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 9 (9.0.100) - Tomcat Developers</title><meta name="author" content="Remy Maucherat"><meta name="author" content="Yoav Shapira"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 9</h1><div class="versionInfo">
            Version 9.0.100,
            <time datetime="2025-02-13">Feb 13 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 4.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 2.3 Javadocs</a></li><li><a href="elapi/index.html">EL 3.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 1.1 Javadocs</a></li><li><a href="jaspicapi/index.html">JASPIC 1.1 Javadocs</a></li><li><a href="annotationapi/index.html">Common Annotations 1.3 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Tomcat Developers</h2><h3 id="Active_Developers">Active Developers</h3><div class="text">

    <p>
      The list indicates the developers' main areas of interest. Feel free to
      add to the list :) The developers email addresses are
      <code>[login]@apache.org</code>. Please <strong>do not</strong> contact
      developers directly for any support issues (please post to the
      tomcat-users mailing list instead, or one of the other support
      resources; some organizations and individual consultants also offer
      for pay Tomcat support, as listed on the
      <a href="https://cwiki.apache.org/confluence/display/TOMCAT/SupportAndTraining">support and
      training</a> page on the Tomcat Wiki).
    </p>

    <ul>
      <li>Bill Barker (billbarker): Connectors</li>
      <li>Costin Manolache (costin): Catalina, Connectors</li>
      <li>Filip Hanik (fhanik): Clustering, Release Manager</li>
      <li>Jean-Frederic Clere (jfclere): Connectors</li>
      <li>Jim Jagielski (jim): Connectors</li>
      <li>Konstantin Kolinko (kkolinko): Catalina</li>
      <li>Mark Thomas (markt): CGI, SSI, WebDAV, bug fixing</li>
      <li>Mladen Turk (mturk): Connectors</li>
      <li>Peter Rossbach (pero): Catalina, Clustering, JMX</li>
      <li>Rainer Jung (rjung): Catalina, Clustering, Connectors</li>
      <li>Remy Maucherat (remm): Catalina, Connectors, Docs</li>
      <li>Tim Funk (funkman): Catalina, Docs</li>
      <li>Tim Whittington (timw): Connectors</li>
    </ul>

  </div><h3 id="Retired_Developers">Retired Developers</h3><div class="text">

    <ul>
      <li>Amy Roh (amyroh): Catalina</li>
      <li>Glenn Nielsen (glenn): Catalina, Connectors</li>
      <li>Henri Gomez (hgomez): Connectors</li>
      <li>Jan Luehe (luehe): Jasper</li>
      <li>Jean-Francois Arcand (jfarcand): Catalina</li>
      <li>Kin-Man Chung (kinman): Jasper</li>
      <li>Yoav Shapira (yoavs): Docs, JMX, Catalina, balancer</li>
    </ul>
  </div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>