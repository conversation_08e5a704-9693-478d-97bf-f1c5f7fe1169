package com.school.management.service;

import com.school.management.entity.DevelopmentTarget;
import java.util.List;
import java.util.Map;

/**
 * 发展对象Service接口
 * 继承基础Service接口，并添加特定的业务方法
 */
public interface DevelopmentTargetService extends BaseService<DevelopmentTarget> {
    
    /**
     * 根据学号查询发展对象
     * @param studentId 学号
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget getByStudentId(String studentId);
    
    /**
     * 根据身份证号查询发展对象
     * @param idCard 身份证号
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget getByIdCard(String idCard);
    
    /**
     * 根据积极分子ID查询发展对象
     * @param activistId 积极分子ID
     * @return 发展对象，不存在返回null
     */
    DevelopmentTarget getByActivistId(Integer activistId);
    
    /**
     * 根据姓名模糊查询发展对象
     * @param name 姓名关键字
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByNameLike(String name);
    
    /**
     * 根据院系查询发展对象
     * @param department 院系名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByDepartment(String department);
    
    /**
     * 根据专业查询发展对象
     * @param major 专业名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByMajor(String major);
    
    /**
     * 根据班级查询发展对象
     * @param className 班级名称
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByClass(String className);
    
    /**
     * 根据状态查询发展对象
     * @param status 状态
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByStatus(String status);
    
    /**
     * 根据政治审查结果查询发展对象
     * @param result 政治审查结果
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByPoliticalReviewResult(String result);
    
    /**
     * 根据确定日期范围查询发展对象
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByDeterminationDateRange(String startDate, String endDate);
    
    /**
     * 根据培训成绩范围查询发展对象
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByTrainingScoreRange(Double minScore, Double maxScore);
    
    /**
     * 根据多个条件组合查询发展对象
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param politicalReviewResult 政治审查结果（可为null）
     * @return 发展对象列表
     */
    List<DevelopmentTarget> getByMultipleConditions(String name, String department, 
                                                   String major, String className, String status,
                                                   String politicalReviewResult);
    
    /**
     * 根据多个条件组合分页查询发展对象
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param politicalReviewResult 政治审查结果（可为null）
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 分页结果对象
     */
    BaseService.PageResult<DevelopmentTarget> getByMultipleConditionsWithPage(String name, String department,
                                                                 String major, String className, String status,
                                                                 String politicalReviewResult,
                                                                 int pageNum, int pageSize);
    
    /**
     * 获取所有院系列表
     * @return 院系名称列表
     */
    List<String> getAllDepartments();
    
    /**
     * 获取指定院系的所有专业列表
     * @param department 院系名称
     * @return 专业名称列表
     */
    List<String> getMajorsByDepartment(String department);
    
    /**
     * 获取指定专业的所有班级列表
     * @param major 专业名称
     * @return 班级名称列表
     */
    List<String> getClassesByMajor(String major);
    
    /**
     * 统计各状态的发展对象数量
     * @return 状态统计Map，key为状态名称，value为数量
     */
    Map<String, Integer> getCountByStatus();
    
    /**
     * 统计各政治审查结果的发展对象数量
     * @return 政治审查结果统计Map
     */
    Map<String, Integer> getCountByPoliticalReviewResult();
    
    /**
     * 获取培训成绩统计信息
     * @return 包含平均分、最高分、最低分等统计信息的Map
     */
    Map<String, Object> getTrainingScoreStatistics();
    
    /**
     * 检查学号是否已存在
     * @param studentId 学号
     * @return 存在返回true，否则返回false
     */
    boolean isStudentIdExists(String studentId);
    
    /**
     * 检查身份证号是否已存在
     * @param idCard 身份证号
     * @return 存在返回true，否则返回false
     */
    boolean isIdCardExists(String idCard);
    
    /**
     * 检查学号是否已存在（排除指定ID）
     * @param studentId 学号
     * @param excludeId 排除的ID
     * @return 存在返回true，否则返回false
     */
    boolean isStudentIdExists(String studentId, Integer excludeId);
    
    /**
     * 检查身份证号是否已存在（排除指定ID）
     * @param idCard 身份证号
     * @param excludeId 排除的ID
     * @return 存在返回true，否则返回false
     */
    boolean isIdCardExists(String idCard, Integer excludeId);
    
    /**
     * 政治审查
     * @param id 发展对象ID
     * @param result 审查结果
     * @param remarks 审查备注
     * @return 操作是否成功
     */
    boolean politicalReview(Integer id, String result, String remarks);
    
    /**
     * 批量政治审查
     * @param ids 发展对象ID数组
     * @param result 审查结果
     * @param remarks 审查备注
     * @return 成功审查的数量
     */
    int batchPoliticalReview(Integer[] ids, String result, String remarks);
    
    /**
     * 更新培训成绩
     * @param id 发展对象ID
     * @param score 培训成绩
     * @return 操作是否成功
     */
    boolean updateTrainingScore(Integer id, Double score);
    
    /**
     * 转为预备党员
     * @param id 发展对象ID
     * @return 操作是否成功
     */
    boolean convertToProbationaryMember(Integer id);
    
    /**
     * 批量转为预备党员
     * @param ids 发展对象ID数组
     * @return 成功转换的数量
     */
    int batchConvertToProbationaryMember(Integer[] ids);
    
    /**
     * 导出发展对象数据
     * @param conditions 查询条件
     * @return 发展对象列表
     */
    List<DevelopmentTarget> exportData(Map<String, Object> conditions);
    
    /**
     * 获取发展对象统计信息
     * @return 统计信息Map
     */
    Map<String, Object> getStatistics();
}
