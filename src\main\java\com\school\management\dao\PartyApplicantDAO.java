package com.school.management.dao;

import com.school.management.entity.PartyApplicant;
import java.util.List;

/**
 * 入党申请人DAO接口
 * 继承基础DAO接口，并添加特定的查询方法
 */
public interface PartyApplicantDAO extends BaseDAO<PartyApplicant> {
    
    /**
     * 根据学号查询申请人
     * @param studentId 学号
     * @return 申请人对象，不存在返回null
     */
    PartyApplicant selectByStudentId(String studentId);
    
    /**
     * 根据身份证号查询申请人
     * @param idCard 身份证号
     * @return 申请人对象，不存在返回null
     */
    PartyApplicant selectByIdCard(String idCard);
    
    /**
     * 根据姓名模糊查询申请人
     * @param name 姓名关键字
     * @return 申请人列表
     */
    List<PartyApplicant> selectByNameLike(String name);
    
    /**
     * 根据院系查询申请人
     * @param department 院系名称
     * @return 申请人列表
     */
    List<PartyApplicant> selectByDepartment(String department);
    
    /**
     * 根据专业查询申请人
     * @param major 专业名称
     * @return 申请人列表
     */
    List<PartyApplicant> selectByMajor(String major);
    
    /**
     * 根据班级查询申请人
     * @param className 班级名称
     * @return 申请人列表
     */
    List<PartyApplicant> selectByClass(String className);
    
    /**
     * 根据申请状态查询申请人
     * @param status 申请状态
     * @return 申请人列表
     */
    List<PartyApplicant> selectByStatus(String status);
    
    /**
     * 根据申请日期范围查询申请人
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 申请人列表
     */
    List<PartyApplicant> selectByApplicationDateRange(String startDate, String endDate);
    
    /**
     * 根据多个条件组合查询申请人
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @return 申请人列表
     */
    List<PartyApplicant> selectByMultipleConditions(String name, String department, 
                                                   String major, String className, String status);
    
    /**
     * 根据多个条件组合分页查询申请人
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 申请人列表
     */
    List<PartyApplicant> selectByMultipleConditionsWithPage(String name, String department, 
                                                           String major, String className, String status,
                                                           int offset, int limit);
    
    /**
     * 根据多个条件统计申请人数量
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @return 申请人数量
     */
    int countByMultipleConditions(String name, String department, 
                                 String major, String className, String status);
    
    /**
     * 获取所有院系列表
     * @return 院系名称列表
     */
    List<String> selectAllDepartments();
    
    /**
     * 获取指定院系的所有专业列表
     * @param department 院系名称
     * @return 专业名称列表
     */
    List<String> selectMajorsByDepartment(String department);
    
    /**
     * 获取指定专业的所有班级列表
     * @param major 专业名称
     * @return 班级名称列表
     */
    List<String> selectClassesByMajor(String major);
    
    /**
     * 统计各状态的申请人数量
     * @return 状态统计Map，key为状态名称，value为数量
     */
    java.util.Map<String, Integer> countByStatus();
}
