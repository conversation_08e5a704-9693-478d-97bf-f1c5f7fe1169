<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学校党员信息管理系统</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>学校党员信息管理系统</h1>
            <nav class="nav">
                <ul>
                    <li><a href="#" onclick="showModule('applicant')">入党申请人</a></li>
                    <li><a href="#" onclick="showModule('activist')">入党积极分子</a></li>
                    <li><a href="#" onclick="showModule('development')">发展对象</a></li>
                    <li><a href="#" onclick="showModule('probationary')">预备党员</a></li>
                    <li><a href="#" onclick="showModule('formal')">正式党员</a></li>
                    <li><a href="#" onclick="showModule('transfer')">组织关系介绍信</a></li>
                </ul>
            </nav>
        </header>

        <main class="main-content">
            <div id="welcome" class="module active">
                <h2>欢迎使用学校党员信息管理系统</h2>
                <p>请选择左侧菜单进行操作</p>

                <div class="quick-links" style="margin: 20px 0; text-align: center;">
                    <h3>快速链接</h3>
                    <a href="test.html" style="margin: 0 10px; padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;">功能测试</a>
                    <a href="db-test.html" style="margin: 0 10px; padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;">数据库测试</a>
                    <a href="java-error-check.html" style="margin: 0 10px; padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;">Java错误检查</a>
                    <a href="test-fixes.html" style="margin: 0 10px; padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;">Bug修复验证</a>
                </div>
                <div class="stats">
                    <div class="stat-card">
                        <h3>入党申请人</h3>
                        <span id="applicant-count">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>入党积极分子</h3>
                        <span id="activist-count">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>发展对象</h3>
                        <span id="development-count">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>预备党员</h3>
                        <span id="probationary-count">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>正式党员</h3>
                        <span id="formal-count">0</span>
                    </div>
                </div>
            </div>

            <!-- 各个模块的内容将通过JavaScript动态加载 -->
            <div id="module-content"></div>
        </main>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
