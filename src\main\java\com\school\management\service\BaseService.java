package com.school.management.service;

import java.util.List;
import java.util.Map;

/**
 * 基础Service接口
 * 定义通用的业务逻辑操作
 * @param <T> 实体类型
 */
public interface BaseService<T> {
    
    /**
     * 添加记录
     * @param entity 实体对象
     * @return 操作结果，成功返回生成的ID，失败返回null
     */
    Integer add(T entity);
    
    /**
     * 根据ID删除记录
     * @param id 主键ID
     * @return 操作是否成功
     */
    boolean deleteById(Integer id);
    
    /**
     * 批量删除记录
     * @param ids 主键ID数组
     * @return 删除的记录数
     */
    int deleteByIds(Integer[] ids);
    
    /**
     * 更新记录
     * @param entity 实体对象
     * @return 操作是否成功
     */
    boolean update(T entity);
    
    /**
     * 根据ID查询记录
     * @param id 主键ID
     * @return 实体对象，不存在返回null
     */
    T getById(Integer id);
    
    /**
     * 查询所有记录
     * @return 实体对象列表
     */
    List<T> getAll();
    
    /**
     * 分页查询记录
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 分页结果对象
     */
    PageResult<T> getByPage(int pageNum, int pageSize);
    
    /**
     * 根据条件查询记录
     * @param conditions 查询条件Map
     * @return 实体对象列表
     */
    List<T> getByConditions(Map<String, Object> conditions);
    
    /**
     * 根据条件分页查询记录
     * @param conditions 查询条件Map
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 分页结果对象
     */
    PageResult<T> getByConditionsWithPage(Map<String, Object> conditions, int pageNum, int pageSize);
    
    /**
     * 统计总记录数
     * @return 总记录数
     */
    int count();
    
    /**
     * 根据条件统计记录数
     * @param conditions 查询条件Map
     * @return 记录数
     */
    int countByConditions(Map<String, Object> conditions);
    
    /**
     * 检查记录是否存在
     * @param id 主键ID
     * @return 存在返回true，否则返回false
     */
    boolean exists(Integer id);
    
    /**
     * 根据字段值检查记录是否存在
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @return 存在返回true，否则返回false
     */
    boolean existsByField(String fieldName, Object fieldValue);
    
    /**
     * 验证实体数据
     * @param entity 实体对象
     * @return 验证结果，成功返回null，失败返回错误信息
     */
    String validate(T entity);
    
    /**
     * 分页结果内部类
     * @param <T> 实体类型
     */
    static class PageResult<T> {
        private List<T> data;           // 当前页数据
        private int pageNum;            // 当前页码
        private int pageSize;           // 每页记录数
        private long total;             // 总记录数
        private int totalPages;         // 总页数
        private boolean hasNext;        // 是否有下一页
        private boolean hasPrevious;    // 是否有上一页
        
        public PageResult() {}
        
        public PageResult(List<T> data, int pageNum, int pageSize, long total) {
            this.data = data;
            this.pageNum = pageNum;
            this.pageSize = pageSize;
            this.total = total;
            this.totalPages = (int) Math.ceil((double) total / pageSize);
            this.hasNext = pageNum < totalPages;
            this.hasPrevious = pageNum > 1;
        }
        
        // Getter和Setter方法
        public List<T> getData() { return data; }
        public void setData(List<T> data) { this.data = data; }
        
        public int getPageNum() { return pageNum; }
        public void setPageNum(int pageNum) { this.pageNum = pageNum; }
        
        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }
        
        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }
        
        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
        
        public boolean isHasNext() { return hasNext; }
        public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }
        
        public boolean isHasPrevious() { return hasPrevious; }
        public void setHasPrevious(boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    }
}
