<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="java.sql.*" %>
<%@ page import="com.school.management.util.DBUtil" %>
<%@ page import="com.school.management.entity.PartyApplicant" %>
<%@ page import="com.school.management.service.impl.PartyApplicantServiceImpl" %>
<%@ page import="com.school.management.service.PartyApplicantService" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能测试 - 学校党员信息管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统功能测试</h1>
        <p class="info">测试时间: <%= new Date() %></p>
        
        <!-- 数据库连接测试 -->
        <div class="test-section">
            <h2 class="test-title">1. 数据库连接测试</h2>
            <%
                boolean dbConnected = false;
                String dbMessage = "";
                try {
                    Connection conn = DBUtil.getConnection();
                    if (conn != null && !conn.isClosed()) {
                        dbConnected = true;
                        dbMessage = "数据库连接成功！";
                        conn.close();
                    } else {
                        dbMessage = "数据库连接失败：连接为空或已关闭";
                    }
                } catch (Exception e) {
                    dbMessage = "数据库连接失败：" + e.getMessage();
                }
            %>
            <p class="<%= dbConnected ? "success" : "error" %>">
                <%= dbMessage %>
            </p>
        </div>
        
        <!-- 表结构检查 -->
        <div class="test-section">
            <h2 class="test-title">2. 数据库表结构检查</h2>
            <%
                Map<String, Boolean> tableStatus = new HashMap<>();
                String[] tables = {"party_applicant", "party_activist"};
                
                try (Connection conn = DBUtil.getConnection()) {
                    DatabaseMetaData metaData = conn.getMetaData();
                    
                    for (String tableName : tables) {
                        try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                            tableStatus.put(tableName, rs.next());
                        }
                    }
                } catch (Exception e) {
                    out.println("<p class='error'>检查表结构失败：" + e.getMessage() + "</p>");
                }
                
                for (Map.Entry<String, Boolean> entry : tableStatus.entrySet()) {
                    String status = entry.getValue() ? "存在" : "不存在";
                    String cssClass = entry.getValue() ? "success" : "error";
            %>
            <p class="<%= cssClass %>">表 <%= entry.getKey() %>: <%= status %></p>
            <% } %>
        </div>
        
        <!-- 服务层测试 -->
        <div class="test-section">
            <h2 class="test-title">3. 服务层功能测试</h2>
            <%
                boolean serviceWorking = false;
                String serviceMessage = "";
                try {
                    PartyApplicantService service = new PartyApplicantServiceImpl();
                    
                    // 测试获取所有申请人
                    List<PartyApplicant> applicants = service.getAll();
                    serviceWorking = true;
                    serviceMessage = "服务层工作正常，当前申请人数量：" + (applicants != null ? applicants.size() : 0);
                    
                } catch (Exception e) {
                    serviceMessage = "服务层测试失败：" + e.getMessage();
                }
            %>
            <p class="<%= serviceWorking ? "success" : "error" %>">
                <%= serviceMessage %>
            </p>
        </div>
        
        <!-- API接口测试 -->
        <div class="test-section">
            <h2 class="test-title">4. API接口测试</h2>
            <p class="info">API接口需要通过AJAX调用测试，请点击下面的按钮进行测试：</p>
            <button class="btn btn-primary" onclick="testAPI()">测试申请人列表API</button>
            <div id="apiResult" style="margin-top: 10px;"></div>
        </div>
        
        <!-- 系统信息 -->
        <div class="test-section">
            <h2 class="test-title">5. 系统信息</h2>
            <pre>
Java版本: <%= System.getProperty("java.version") %>
操作系统: <%= System.getProperty("os.name") %> <%= System.getProperty("os.version") %>
服务器信息: <%= application.getServerInfo() %>
项目路径: <%= request.getContextPath() %>
当前时间: <%= new Date() %>
            </pre>
        </div>
        
        <!-- 功能链接 -->
        <div class="test-section">
            <h2 class="test-title">6. 功能页面链接</h2>
            <a href="<%=request.getContextPath()%>/index.jsp" class="btn btn-info">主页</a>
            <a href="<%=request.getContextPath()%>/applicant-management.jsp" class="btn btn-success">申请人管理</a>
            <a href="<%=request.getContextPath()%>/db-test.jsp" class="btn btn-primary">数据库测试</a>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <h2 class="test-title">7. 测试建议</h2>
            <ul>
                <li class="info">✓ 确保MySQL数据库服务正在运行</li>
                <li class="info">✓ 确保数据库名称为'management'</li>
                <li class="info">✓ 确保数据库用户名密码正确（root/123456）</li>
                <li class="info">✓ 执行database.sql脚本创建表结构</li>
                <li class="warning">⚠ 如果API测试失败，检查Servlet映射配置</li>
                <li class="warning">⚠ 如果页面显示异常，检查JSP编译环境</li>
            </ul>
        </div>
    </div>

    <script>
        function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p class="info">正在测试API...</p>';
            
            fetch('<%=request.getContextPath()%>/api/applicant/list')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = '<p class="success">API测试成功！返回数据：' + JSON.stringify(data, null, 2) + '</p>';
                    } else {
                        resultDiv.innerHTML = '<p class="error">API返回错误：' + data.message + '</p>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<p class="error">API测试失败：' + error.message + '</p>';
                });
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('系统测试页面加载完成');
            console.log('如果看到这条消息，说明JavaScript正常工作');
        });
    </script>
</body>
</html>
