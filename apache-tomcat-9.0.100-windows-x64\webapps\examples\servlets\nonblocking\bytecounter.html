<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<html>
  <head>
    <title>Servlet 3.1 non-blocking IO examples: Byte counter</title>
  </head>
  <body>
    <h1>Byte counter</h1>
    <p>Select a file and/or enter some data using the form below and then submit
       it. The server will read the request body using non-blocking IO and then
       respond with the total length of the request body in bytes.</p>
    <form method="POST" enctype="multipart/form-data" action="bytecounter" >
      <p><textarea name="data" rows="5" cols="60" ></textarea></p>
      <p><input name="source" type="file" /></p>
      <p><input type="submit" value="Submit" /></p>
    </form>
  </body>
</html>