package com.school.management.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 预备党员实体类
 */
public class ProbationaryMember {
    
    private Integer id;                    // 主键ID
    private Integer developmentId;         // 发展对象ID
    private String name;                   // 姓名
    private String gender;                 // 性别
    private Date birthDate;                // 出生日期
    private String idCard;                 // 身份证号
    private String phone;                  // 联系电话
    private String email;                  // 邮箱
    private String department;             // 所在院系
    private String major;                  // 专业
    private String className;              // 班级
    private String studentId;              // 学号
    private Date admissionDate;            // 入党日期
    private Date probationStartDate;       // 预备期开始日期
    private Date probationEndDate;         // 预备期结束日期
    private String partyBranch;            // 所在党支部
    private String introducer1Name;        // 入党介绍人1
    private String introducer2Name;        // 入党介绍人2
    private String quarterlyReport1;       // 第一季度思想汇报
    private String quarterlyReport2;       // 第二季度思想汇报
    private String quarterlyReport3;       // 第三季度思想汇报
    private String quarterlyReport4;       // 第四季度思想汇报
    private String performanceEvaluation;  // 预备期表现评价
    private String status;                 // 状态
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public ProbationaryMember() {}
    
    // 全参构造函数
    public ProbationaryMember(Integer id, Integer developmentId, String name, String gender, Date birthDate,
                            String idCard, String phone, String email, String department, String major,
                            String className, String studentId, Date admissionDate, Date probationStartDate,
                            Date probationEndDate, String partyBranch, String introducer1Name, String introducer2Name,
                            String quarterlyReport1, String quarterlyReport2, String quarterlyReport3, String quarterlyReport4,
                            String performanceEvaluation, String status, String remarks,
                            Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.developmentId = developmentId;
        this.name = name;
        this.gender = gender;
        this.birthDate = birthDate;
        this.idCard = idCard;
        this.phone = phone;
        this.email = email;
        this.department = department;
        this.major = major;
        this.className = className;
        this.studentId = studentId;
        this.admissionDate = admissionDate;
        this.probationStartDate = probationStartDate;
        this.probationEndDate = probationEndDate;
        this.partyBranch = partyBranch;
        this.introducer1Name = introducer1Name;
        this.introducer2Name = introducer2Name;
        this.quarterlyReport1 = quarterlyReport1;
        this.quarterlyReport2 = quarterlyReport2;
        this.quarterlyReport3 = quarterlyReport3;
        this.quarterlyReport4 = quarterlyReport4;
        this.performanceEvaluation = performanceEvaluation;
        this.status = status;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public Integer getDevelopmentId() { return developmentId; }
    public void setDevelopmentId(Integer developmentId) { this.developmentId = developmentId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }
    
    public Date getBirthDate() { return birthDate; }
    public void setBirthDate(Date birthDate) { this.birthDate = birthDate; }
    
    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) { this.idCard = idCard; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public String getMajor() { return major; }
    public void setMajor(String major) { this.major = major; }
    
    public String getClassName() { return className; }
    public void setClassName(String className) { this.className = className; }
    
    public String getStudentId() { return studentId; }
    public void setStudentId(String studentId) { this.studentId = studentId; }
    
    public Date getAdmissionDate() { return admissionDate; }
    public void setAdmissionDate(Date admissionDate) { this.admissionDate = admissionDate; }
    
    public Date getProbationStartDate() { return probationStartDate; }
    public void setProbationStartDate(Date probationStartDate) { this.probationStartDate = probationStartDate; }
    
    public Date getProbationEndDate() { return probationEndDate; }
    public void setProbationEndDate(Date probationEndDate) { this.probationEndDate = probationEndDate; }
    
    public String getPartyBranch() { return partyBranch; }
    public void setPartyBranch(String partyBranch) { this.partyBranch = partyBranch; }
    
    public String getIntroducer1Name() { return introducer1Name; }
    public void setIntroducer1Name(String introducer1Name) { this.introducer1Name = introducer1Name; }
    
    public String getIntroducer2Name() { return introducer2Name; }
    public void setIntroducer2Name(String introducer2Name) { this.introducer2Name = introducer2Name; }
    
    public String getQuarterlyReport1() { return quarterlyReport1; }
    public void setQuarterlyReport1(String quarterlyReport1) { this.quarterlyReport1 = quarterlyReport1; }
    
    public String getQuarterlyReport2() { return quarterlyReport2; }
    public void setQuarterlyReport2(String quarterlyReport2) { this.quarterlyReport2 = quarterlyReport2; }
    
    public String getQuarterlyReport3() { return quarterlyReport3; }
    public void setQuarterlyReport3(String quarterlyReport3) { this.quarterlyReport3 = quarterlyReport3; }
    
    public String getQuarterlyReport4() { return quarterlyReport4; }
    public void setQuarterlyReport4(String quarterlyReport4) { this.quarterlyReport4 = quarterlyReport4; }
    
    public String getPerformanceEvaluation() { return performanceEvaluation; }
    public void setPerformanceEvaluation(String performanceEvaluation) { this.performanceEvaluation = performanceEvaluation; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }
    
    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "ProbationaryMember{" +
                "id=" + id +
                ", developmentId=" + developmentId +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", idCard='" + idCard + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", department='" + department + '\'' +
                ", major='" + major + '\'' +
                ", className='" + className + '\'' +
                ", studentId='" + studentId + '\'' +
                ", admissionDate=" + admissionDate +
                ", probationStartDate=" + probationStartDate +
                ", probationEndDate=" + probationEndDate +
                ", partyBranch='" + partyBranch + '\'' +
                ", introducer1Name='" + introducer1Name + '\'' +
                ", introducer2Name='" + introducer2Name + '\'' +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
