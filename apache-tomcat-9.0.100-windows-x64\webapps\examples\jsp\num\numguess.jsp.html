<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

  Number Guess Game
  Written by <PERSON>, CTO, K&amp;A Software
  http://www.servlets.com
--%>

&lt;%@ page import = "num.NumberGuessBean" %>

&lt;jsp:useBean id="numguess" class="num.NumberGuessBean" scope="session"/>
&lt;jsp:setProperty name="numguess" property="*"/>

&lt;html>
&lt;head>&lt;title>Number Guess&lt;/title>&lt;/head>
&lt;body bgcolor="white">
&lt;font size=4>

&lt;% if (numguess.getSuccess()) { %>

  Congratulations!  You got it.
  And after just &lt;%= numguess.getNumGuesses() %> tries.&lt;p>

  &lt;% numguess.reset(); %>

  Care to &lt;a href="numguess.jsp">try again&lt;/a>?

&lt;% } else if (numguess.getNumGuesses() == 0) { %>

  Welcome to the Number Guess game.&lt;p>

  I'm thinking of a number between 1 and 100.&lt;p>

  &lt;form method=get>
  What's your guess? &lt;input type=text name=guess>
  &lt;input type=submit value="Submit">
  &lt;/form>

&lt;% } else { %>

  Good guess, but nope.  Try &lt;b>&lt;%= numguess.getHint() %>&lt;/b>.

  You have made &lt;%= numguess.getNumGuesses() %> guesses.&lt;p>

  I'm thinking of a number between 1 and 100.&lt;p>

  &lt;form method=get>
  What's your guess? &lt;input type=text name=guess>
  &lt;input type=submit value="Submit">
  &lt;/form>

&lt;% } %>

&lt;/font>
&lt;/body>
&lt;/html>
</pre></body></html>