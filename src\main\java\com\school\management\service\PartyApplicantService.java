package com.school.management.service;

import com.school.management.entity.PartyApplicant;
import java.util.List;
import java.util.Map;

/**
 * 入党申请人Service接口
 * 继承基础Service接口，并添加特定的业务方法
 */
public interface PartyApplicantService extends BaseService<PartyApplicant> {
    
    /**
     * 根据学号查询申请人
     * @param studentId 学号
     * @return 申请人对象，不存在返回null
     */
    PartyApplicant getByStudentId(String studentId);
    
    /**
     * 根据身份证号查询申请人
     * @param idCard 身份证号
     * @return 申请人对象，不存在返回null
     */
    PartyApplicant getByIdCard(String idCard);
    
    /**
     * 根据姓名模糊查询申请人
     * @param name 姓名关键字
     * @return 申请人列表
     */
    List<PartyApplicant> getByNameLike(String name);
    
    /**
     * 根据院系查询申请人
     * @param department 院系名称
     * @return 申请人列表
     */
    List<PartyApplicant> getByDepartment(String department);
    
    /**
     * 根据专业查询申请人
     * @param major 专业名称
     * @return 申请人列表
     */
    List<PartyApplicant> getByMajor(String major);
    
    /**
     * 根据班级查询申请人
     * @param className 班级名称
     * @return 申请人列表
     */
    List<PartyApplicant> getByClass(String className);
    
    /**
     * 根据申请状态查询申请人
     * @param status 申请状态
     * @return 申请人列表
     */
    List<PartyApplicant> getByStatus(String status);
    
    /**
     * 根据申请日期范围查询申请人
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 申请人列表
     */
    List<PartyApplicant> getByApplicationDateRange(String startDate, String endDate);
    
    /**
     * 根据条件Map查询申请人
     * @param conditions 查询条件Map
     * @return 申请人列表
     */
    List<PartyApplicant> getByConditions(Map<String, Object> conditions);

    /**
     * 根据多个条件组合查询申请人
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @return 申请人列表
     */
    List<PartyApplicant> getByMultipleConditions(String name, String department,
                                                String major, String className, String status);
    
    /**
     * 根据多个条件组合分页查询申请人
     * @param name 姓名（可为null）
     * @param department 院系（可为null）
     * @param major 专业（可为null）
     * @param className 班级（可为null）
     * @param status 状态（可为null）
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 分页结果对象
     */
    BaseService.PageResult<PartyApplicant> getByMultipleConditionsWithPage(String name, String department,
                                                              String major, String className, String status,
                                                              int pageNum, int pageSize);
    
    /**
     * 获取所有院系列表
     * @return 院系名称列表
     */
    List<String> getAllDepartments();
    
    /**
     * 获取指定院系的所有专业列表
     * @param department 院系名称
     * @return 专业名称列表
     */
    List<String> getMajorsByDepartment(String department);
    
    /**
     * 获取指定专业的所有班级列表
     * @param major 专业名称
     * @return 班级名称列表
     */
    List<String> getClassesByMajor(String major);
    
    /**
     * 统计各状态的申请人数量
     * @return 状态统计Map，key为状态名称，value为数量
     */
    Map<String, Integer> getCountByStatus();
    
    /**
     * 检查学号是否已存在
     * @param studentId 学号
     * @return 存在返回true，否则返回false
     */
    boolean isStudentIdExists(String studentId);
    
    /**
     * 检查身份证号是否已存在
     * @param idCard 身份证号
     * @return 存在返回true，否则返回false
     */
    boolean isIdCardExists(String idCard);
    
    /**
     * 检查学号是否已存在（排除指定ID）
     * @param studentId 学号
     * @param excludeId 排除的ID
     * @return 存在返回true，否则返回false
     */
    boolean isStudentIdExists(String studentId, Integer excludeId);
    
    /**
     * 检查身份证号是否已存在（排除指定ID）
     * @param idCard 身份证号
     * @param excludeId 排除的ID
     * @return 存在返回true，否则返回false
     */
    boolean isIdCardExists(String idCard, Integer excludeId);
    
    /**
     * 审核申请人
     * @param id 申请人ID
     * @param status 审核状态（已通过、已拒绝）
     * @param remarks 审核备注
     * @return 操作是否成功
     */
    boolean approve(Integer id, String status, String remarks);
    
    /**
     * 批量审核申请人
     * @param ids 申请人ID数组
     * @param status 审核状态
     * @param remarks 审核备注
     * @return 成功审核的数量
     */
    int batchApprove(Integer[] ids, String status, String remarks);
    
    /**
     * 导出申请人数据
     * @param conditions 查询条件
     * @return 申请人列表
     */
    List<PartyApplicant> exportData(Map<String, Object> conditions);
    
    /**
     * 获取申请人统计信息
     * @return 统计信息Map
     */
    Map<String, Object> getStatistics();
}
