// 入党申请人模块JavaScript

// 创建模块命名空间
window.ApplicantModule = window.ApplicantModule || {};

// 模块内部变量
ApplicantModule.currentPage = 1;
ApplicantModule.pageSize = 10;
ApplicantModule.currentSearchConditions = {};

// 加载入党申请人模块内容
window.loadApplicantModuleContent = function() {
    const content = `
        <div class="module-header">
            <h2>入党申请人管理</h2>
            <div class="module-actions">
                <button class="btn btn-primary" onclick="showAddApplicantForm()">
                    <i class="icon-plus"></i> 添加申请人
                </button>
                <button class="btn btn-success" onclick="exportApplicantData()">
                    <i class="icon-export"></i> 导出数据
                </button>
            </div>
        </div>

        <div class="search-container">
            <div class="search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名:</label>
                        <input type="text" id="searchName" placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label>院系:</label>
                        <select id="searchDepartment">
                            <option value="">请选择院系</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>专业:</label>
                        <select id="searchMajor">
                            <option value="">请选择专业</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>班级:</label>
                        <select id="searchClass">
                            <option value="">请选择班级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>状态:</label>
                        <select id="searchStatus">
                            <option value="">全部状态</option>
                            <option value="待审核">待审核</option>
                            <option value="已通过">已通过</option>
                            <option value="已拒绝">已拒绝</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="searchApplicants()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table id="applicantTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>学号</th>
                        <th>院系</th>
                        <th>专业</th>
                        <th>班级</th>
                        <th>申请日期</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="applicantTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">共 0 条记录</span>
            </div>
            <div class="pagination">
                <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn">上一页</button>
                <span id="pageNumbers"></span>
                <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">下一页</button>
            </div>
        </div>

        <div class="batch-actions">
            <button class="btn btn-success" onclick="batchApprove('已通过')">批量通过</button>
            <button class="btn btn-warning" onclick="batchApprove('已拒绝')">批量拒绝</button>
            <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
        </div>

        <!-- 添加/编辑表单模态框 -->
        <div id="applicantModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">添加入党申请人</h3>
                    <span class="close" onclick="closeApplicantModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="applicantForm">
                        <input type="hidden" id="applicantId">
                        <div class="form-row">
                            <div class="form-group">
                                <label>姓名 <span class="required">*</span>:</label>
                                <input type="text" id="name" required>
                            </div>
                            <div class="form-group">
                                <label>性别 <span class="required">*</span>:</label>
                                <select id="gender" required>
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>出生日期 <span class="required">*</span>:</label>
                                <input type="date" id="birthDate" required>
                            </div>
                            <div class="form-group">
                                <label>身份证号 <span class="required">*</span>:</label>
                                <input type="text" id="idCard" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>联系电话:</label>
                                <input type="text" id="phone">
                            </div>
                            <div class="form-group">
                                <label>邮箱:</label>
                                <input type="email" id="email">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>院系 <span class="required">*</span>:</label>
                                <select id="department" required onchange="loadMajorsByDepartment()">
                                    <option value="">请选择院系</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>专业 <span class="required">*</span>:</label>
                                <select id="major" required onchange="loadClassesByMajor()">
                                    <option value="">请选择专业</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>班级 <span class="required">*</span>:</label>
                                <select id="className" required>
                                    <option value="">请选择班级</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>学号 <span class="required">*</span>:</label>
                                <input type="text" id="studentId" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>申请日期 <span class="required">*</span>:</label>
                                <input type="date" id="applicationDate" required>
                            </div>
                            <div class="form-group">
                                <label>政治面貌:</label>
                                <select id="politicalStatus">
                                    <option value="群众">群众</option>
                                    <option value="共青团员">共青团员</option>
                                    <option value="民主党派">民主党派</option>
                                    <option value="无党派人士">无党派人士</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>籍贯:</label>
                                <input type="text" id="hometown">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>家庭背景:</label>
                                <textarea id="familyBackground" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>个人简历:</label>
                                <textarea id="personalResume" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>入党申请理由:</label>
                                <textarea id="applicationReason" rows="4"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>备注:</label>
                                <textarea id="remarks" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="saveApplicant()">保存</button>
                    <button class="btn btn-secondary" onclick="closeApplicantModal()">取消</button>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('module-content').innerHTML = content;
    
    // 初始化页面
    initApplicantModule();
};

// 初始化模块
function initApplicantModule() {
    // 设置默认申请日期为今天
    document.getElementById('applicationDate').value = new Date().toISOString().split('T')[0];
    
    // 加载基础数据
    loadDepartments();
    loadApplicantData();
}

// 加载院系数据
function loadDepartments() {
    // 这里应该调用后端接口获取院系数据
    // 暂时使用模拟数据
    const departments = ['计算机学院', '电子信息学院', '机械工程学院', '经济管理学院', '外国语学院'];
    
    const searchDepartmentSelect = document.getElementById('searchDepartment');
    const departmentSelect = document.getElementById('department');
    
    departments.forEach(dept => {
        const option1 = new Option(dept, dept);
        const option2 = new Option(dept, dept);
        searchDepartmentSelect.add(option1);
        departmentSelect.add(option2);
    });
}

// 根据院系加载专业
function loadMajorsByDepartment() {
    const department = document.getElementById('department').value;
    const majorSelect = document.getElementById('major');
    const classSelect = document.getElementById('className');
    
    // 清空专业和班级选项
    majorSelect.innerHTML = '<option value="">请选择专业</option>';
    classSelect.innerHTML = '<option value="">请选择班级</option>';
    
    if (!department) return;
    
    // 这里应该调用后端接口获取专业数据
    // 暂时使用模拟数据
    const majors = {
        '计算机学院': ['计算机科学与技术', '软件工程', '网络工程', '信息安全'],
        '电子信息学院': ['电子信息工程', '通信工程', '自动化', '电气工程'],
        '机械工程学院': ['机械设计制造及其自动化', '材料成型及控制工程', '工业设计'],
        '经济管理学院': ['工商管理', '会计学', '市场营销', '国际经济与贸易'],
        '外国语学院': ['英语', '日语', '德语', '法语']
    };
    
    const departmentMajors = majors[department] || [];
    departmentMajors.forEach(major => {
        majorSelect.add(new Option(major, major));
    });
}

// 根据专业加载班级
function loadClassesByMajor() {
    const major = document.getElementById('major').value;
    const classSelect = document.getElementById('className');
    
    // 清空班级选项
    classSelect.innerHTML = '<option value="">请选择班级</option>';
    
    if (!major) return;
    
    // 这里应该调用后端接口获取班级数据
    // 暂时使用模拟数据
    const classes = ['2021级1班', '2021级2班', '2022级1班', '2022级2班', '2023级1班', '2023级2班'];
    
    classes.forEach(cls => {
        classSelect.add(new Option(cls, cls));
    });
}

// 加载申请人数据
function loadApplicantData() {
    // 这里应该调用后端接口获取数据
    // 暂时使用模拟数据
    const mockData = {
        data: [
            {
                id: 1,
                name: '张三',
                gender: '男',
                studentId: '2021001',
                department: '计算机学院',
                major: '计算机科学与技术',
                className: '2021级1班',
                applicationDate: '2024-01-15',
                status: '待审核'
            },
            {
                id: 2,
                name: '李四',
                gender: '女',
                studentId: '2021002',
                department: '计算机学院',
                major: '软件工程',
                className: '2021级2班',
                applicationDate: '2024-01-20',
                status: '已通过'
            }
        ],
        total: 2,
        pageNum: 1,
        pageSize: 10,
        totalPages: 1
    };

    displayApplicantData(mockData);
}

// 显示申请人数据
function displayApplicantData(data) {
    const tbody = document.getElementById('applicantTableBody');
    tbody.innerHTML = '';

    if (data.data && data.data.length > 0) {
        data.data.forEach(applicant => {
            const row = `
                <tr>
                    <td><input type="checkbox" class="row-checkbox" value="${applicant.id}"></td>
                    <td>${applicant.name}</td>
                    <td>${applicant.gender}</td>
                    <td>${applicant.studentId}</td>
                    <td>${applicant.department}</td>
                    <td>${applicant.major}</td>
                    <td>${applicant.className}</td>
                    <td>${formatDate(applicant.applicationDate)}</td>
                    <td><span class="status-badge status-${applicant.status}">${applicant.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editApplicant(${applicant.id})">编辑</button>
                        <button class="btn btn-sm btn-success" onclick="viewApplicant(${applicant.id})">查看</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteApplicant(${applicant.id})">删除</button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center">暂无数据</td></tr>';
    }

    // 更新分页信息
    updatePaginationInfo(data);
}

// 更新分页信息
function updatePaginationInfo(data) {
    const paginationInfo = document.getElementById('paginationInfo');
    paginationInfo.textContent = `共 ${data.total} 条记录，第 ${data.pageNum} 页，共 ${data.totalPages} 页`;

    // 更新分页按钮状态
    document.getElementById('prevBtn').disabled = data.pageNum <= 1;
    document.getElementById('nextBtn').disabled = data.pageNum >= data.totalPages;

    // 生成页码
    generatePageNumbers(data.pageNum, data.totalPages);
}

// 生成页码
function generatePageNumbers(currentPage, totalPages) {
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
        pageBtn.textContent = i;
        pageBtn.onclick = () => goToPage(i);
        pageNumbers.appendChild(pageBtn);
    }
}

// 跳转到指定页
function goToPage(page) {
    ApplicantModule.currentPage = page;
    loadApplicantData();
}

// 上一页
function previousPage() {
    if (ApplicantModule.currentPage > 1) {
        ApplicantModule.currentPage--;
        loadApplicantData();
    }
}

// 下一页
function nextPage() {
    ApplicantModule.currentPage++;
    loadApplicantData();
}

// 搜索申请人
function searchApplicants() {
    ApplicantModule.currentSearchConditions = {
        name: document.getElementById('searchName').value,
        department: document.getElementById('searchDepartment').value,
        major: document.getElementById('searchMajor').value,
        className: document.getElementById('searchClass').value,
        status: document.getElementById('searchStatus').value
    };

    ApplicantModule.currentPage = 1;
    loadApplicantData();
}

// 重置搜索
function resetSearch() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchDepartment').value = '';
    document.getElementById('searchMajor').value = '';
    document.getElementById('searchClass').value = '';
    document.getElementById('searchStatus').value = '';

    ApplicantModule.currentSearchConditions = {};
    ApplicantModule.currentPage = 1;
    loadApplicantData();
}

// 显示添加申请人表单
function showAddApplicantForm() {
    document.getElementById('modalTitle').textContent = '添加入党申请人';
    document.getElementById('applicantForm').reset();
    document.getElementById('applicantId').value = '';
    document.getElementById('applicationDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('applicantModal').style.display = 'block';
}

// 编辑申请人
function editApplicant(id) {
    // 这里应该调用后端接口获取申请人详细信息
    // 暂时使用模拟数据
    const mockApplicant = {
        id: id,
        name: '张三',
        gender: '男',
        birthDate: '2000-01-01',
        idCard: '123456789012345678',
        phone: '13800138000',
        email: '<EMAIL>',
        department: '计算机学院',
        major: '计算机科学与技术',
        className: '2021级1班',
        studentId: '2021001',
        applicationDate: '2024-01-15',
        politicalStatus: '共青团员',
        hometown: '北京市',
        familyBackground: '家庭背景信息',
        personalResume: '个人简历信息',
        applicationReason: '入党申请理由',
        remarks: '备注信息'
    };

    // 填充表单
    fillApplicantForm(mockApplicant);

    document.getElementById('modalTitle').textContent = '编辑入党申请人';
    document.getElementById('applicantModal').style.display = 'block';
}

// 填充申请人表单
function fillApplicantForm(applicant) {
    document.getElementById('applicantId').value = applicant.id || '';
    document.getElementById('name').value = applicant.name || '';
    document.getElementById('gender').value = applicant.gender || '';
    document.getElementById('birthDate').value = applicant.birthDate || '';
    document.getElementById('idCard').value = applicant.idCard || '';
    document.getElementById('phone').value = applicant.phone || '';
    document.getElementById('email').value = applicant.email || '';
    document.getElementById('department').value = applicant.department || '';
    document.getElementById('major').value = applicant.major || '';
    document.getElementById('className').value = applicant.className || '';
    document.getElementById('studentId').value = applicant.studentId || '';
    document.getElementById('applicationDate').value = applicant.applicationDate || '';
    document.getElementById('politicalStatus').value = applicant.politicalStatus || '群众';
    document.getElementById('hometown').value = applicant.hometown || '';
    document.getElementById('familyBackground').value = applicant.familyBackground || '';
    document.getElementById('personalResume').value = applicant.personalResume || '';
    document.getElementById('applicationReason').value = applicant.applicationReason || '';
    document.getElementById('remarks').value = applicant.remarks || '';
}

// 查看申请人详情
function viewApplicant(id) {
    // 这里应该调用后端接口获取申请人详细信息
    // 然后显示在只读模式的模态框中
    editApplicant(id);

    // 将表单设置为只读
    const formElements = document.querySelectorAll('#applicantForm input, #applicantForm select, #applicantForm textarea');
    formElements.forEach(element => {
        element.disabled = true;
    });

    document.getElementById('modalTitle').textContent = '查看入党申请人详情';
    document.querySelector('.modal-footer').innerHTML = '<button class="btn btn-secondary" onclick="closeApplicantModal()">关闭</button>';
}

// 保存申请人
function saveApplicant() {
    const form = document.getElementById('applicantForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const applicantData = {
        id: document.getElementById('applicantId').value,
        name: document.getElementById('name').value,
        gender: document.getElementById('gender').value,
        birthDate: document.getElementById('birthDate').value,
        idCard: document.getElementById('idCard').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        department: document.getElementById('department').value,
        major: document.getElementById('major').value,
        className: document.getElementById('className').value,
        studentId: document.getElementById('studentId').value,
        applicationDate: document.getElementById('applicationDate').value,
        politicalStatus: document.getElementById('politicalStatus').value,
        hometown: document.getElementById('hometown').value,
        familyBackground: document.getElementById('familyBackground').value,
        personalResume: document.getElementById('personalResume').value,
        applicationReason: document.getElementById('applicationReason').value,
        remarks: document.getElementById('remarks').value
    };

    // 这里应该调用后端接口保存数据
    console.log('保存申请人数据:', applicantData);

    // 模拟保存成功
    showMessage('保存成功', 'success');
    closeApplicantModal();
    loadApplicantData();
}

// 关闭申请人模态框
function closeApplicantModal() {
    document.getElementById('applicantModal').style.display = 'none';

    // 恢复表单状态
    const formElements = document.querySelectorAll('#applicantForm input, #applicantForm select, #applicantForm textarea');
    formElements.forEach(element => {
        element.disabled = false;
    });

    // 恢复模态框底部按钮
    document.querySelector('.modal-footer').innerHTML = `
        <button class="btn btn-primary" onclick="saveApplicant()">保存</button>
        <button class="btn btn-secondary" onclick="closeApplicantModal()">取消</button>
    `;
}

// 删除申请人
function deleteApplicant(id) {
    confirmAction('确定要删除这个申请人吗？', () => {
        // 这里应该调用后端接口删除数据
        console.log('删除申请人:', id);

        // 模拟删除成功
        showMessage('删除成功', 'success');
        loadApplicantData();
    });
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 获取选中的申请人ID
function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// 批量审核
function batchApprove(status) {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showMessage('请选择要审核的申请人', 'warning');
        return;
    }

    const action = status === '已通过' ? '通过' : '拒绝';
    confirmAction(`确定要批量${action}选中的申请人吗？`, () => {
        // 这里应该调用后端接口批量审核
        console.log(`批量${action}申请人:`, selectedIds);

        // 模拟操作成功
        showMessage(`批量${action}成功`, 'success');
        loadApplicantData();

        // 取消全选
        document.getElementById('selectAll').checked = false;
    });
}

// 批量删除
function batchDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showMessage('请选择要删除的申请人', 'warning');
        return;
    }

    confirmAction('确定要批量删除选中的申请人吗？', () => {
        // 这里应该调用后端接口批量删除
        console.log('批量删除申请人:', selectedIds);

        // 模拟删除成功
        showMessage('批量删除成功', 'success');
        loadApplicantData();

        // 取消全选
        document.getElementById('selectAll').checked = false;
    });
}

// 导出数据
function exportApplicantData() {
    // 这里应该调用后端接口导出数据
    console.log('导出申请人数据');
    showMessage('导出功能开发中...', 'info');
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modal = document.getElementById('applicantModal');
    if (modal && event.target === modal) {
        closeApplicantModal();
    }
});
