// 主要的JavaScript功能文件

// 当前活动的模块
let currentModule = 'welcome';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
});

// 显示指定模块
function showModule(moduleName) {
    // 隐藏欢迎页面
    const welcomeElement = document.getElementById('welcome');
    if (welcomeElement) {
        welcomeElement.style.display = 'none';
    }

    // 显示模块内容区域
    const moduleContentElement = document.getElementById('module-content');
    if (moduleContentElement) {
        moduleContentElement.style.display = 'block';
    }

    // 更新当前模块
    currentModule = moduleName;

    // 加载模块内容
    loadModuleContent(moduleName);
}

// 加载模块内容
function loadModuleContent(moduleName) {
    const moduleContent = document.getElementById('module-content');
    
    // 根据模块名称加载不同的内容
    switch(moduleName) {
        case 'applicant':
            loadApplicantModule();
            break;
        case 'activist':
            loadActivistModule();
            break;
        case 'development':
            loadDevelopmentModule();
            break;
        case 'probationary':
            loadProbationaryModule();
            break;
        case 'formal':
            loadFormalModule();
            break;
        case 'transfer':
            loadTransferModule();
            break;
        default:
            moduleContent.innerHTML = '<h2>模块未找到</h2>';
    }
    
    moduleContent.style.display = 'block';
}

// 加载统计数据
function loadStatistics() {
    // 这里将通过AJAX调用后端接口获取统计数据
    // 暂时使用模拟数据
    updateStatistics({
        applicant: 0,
        activist: 0,
        development: 0,
        probationary: 0,
        formal: 0
    });
}

// 更新统计数据显示
function updateStatistics(stats) {
    document.getElementById('applicant-count').textContent = stats.applicant || 0;
    document.getElementById('activist-count').textContent = stats.activist || 0;
    document.getElementById('development-count').textContent = stats.development || 0;
    document.getElementById('probationary-count').textContent = stats.probationary || 0;
    document.getElementById('formal-count').textContent = stats.formal || 0;
}

// 通用的AJAX请求函数
function makeRequest(url, method, data, callback) {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    callback(null, response);
                } catch (e) {
                    callback(e, null);
                }
            } else {
                callback(new Error('请求失败: ' + xhr.status), null);
            }
        }
    };
    
    if (data) {
        xhr.send(JSON.stringify(data));
    } else {
        xhr.send();
    }
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    // 根据类型设置背景色
    switch(type) {
        case 'success':
            messageDiv.style.backgroundColor = '#28a745';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            messageDiv.style.backgroundColor = '#ffc107';
            messageDiv.style.color = '#212529';
            break;
        default:
            messageDiv.style.backgroundColor = '#17a2b8';
    }
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 验证表单
function validateForm(formData, rules) {
    const errors = [];
    
    for (const field in rules) {
        const rule = rules[field];
        const value = formData[field];
        
        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${rule.label}不能为空`);
        }
        
        if (value && rule.pattern && !rule.pattern.test(value)) {
            errors.push(`${rule.label}格式不正确`);
        }
        
        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`${rule.label}长度不能少于${rule.minLength}个字符`);
        }
        
        if (value && rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${rule.label}长度不能超过${rule.maxLength}个字符`);
        }
    }
    
    return errors;
}

// 返回欢迎页面
function showWelcome() {
    document.getElementById('welcome').style.display = 'block';
    document.getElementById('module-content').style.display = 'none';
    currentModule = 'welcome';
    loadStatistics();
}

// 加载入党申请人模块
function loadApplicantModule() {
    // 动态加载applicant.js
    if (!window.applicantModuleLoaded) {
        const script = document.createElement('script');
        script.src = 'js/applicant.js';
        script.onload = function() {
            window.applicantModuleLoaded = true;
            // 调用applicant.js中的函数
            if (typeof window.loadApplicantModuleContent === 'function') {
                window.loadApplicantModuleContent();
            }
        };
        document.head.appendChild(script);
    } else {
        // 如果已经加载过，直接调用
        if (typeof window.loadApplicantModuleContent === 'function') {
            window.loadApplicantModuleContent();
        }
    }
}

// 其他模块的加载函数（暂时使用占位实现）
function loadActivistModule() {
    document.getElementById('module-content').innerHTML = '<h2>入党积极分子模块</h2><p>功能开发中...</p>';
}

function loadDevelopmentModule() {
    // 动态加载development.js
    if (!window.developmentModuleLoaded) {
        const script = document.createElement('script');
        script.src = 'js/development.js';
        script.onload = function() {
            window.developmentModuleLoaded = true;
            // 调用development.js中的函数
            if (typeof window.loadDevelopmentModuleContent === 'function') {
                window.loadDevelopmentModuleContent();
            }
        };
        document.head.appendChild(script);
    } else {
        // 如果已经加载过，直接调用
        if (typeof window.loadDevelopmentModuleContent === 'function') {
            window.loadDevelopmentModuleContent();
        }
    }
}

function loadProbationaryModule() {
    document.getElementById('module-content').innerHTML = '<h2>预备党员模块</h2><p>功能开发中...</p>';
}

function loadFormalModule() {
    document.getElementById('module-content').innerHTML = '<h2>正式党员模块</h2><p>功能开发中...</p>';
}

function loadTransferModule() {
    document.getElementById('module-content').innerHTML = '<h2>组织关系介绍信模块</h2><p>功能开发中...</p>';
}

// 工具函数

/**
 * 显示消息提示
 * @param {string} message 消息内容
 * @param {string} type 消息类型：success, error, warning, info
 */
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.innerHTML = `
        <span>${message}</span>
        <button class="message-close" onclick="this.parentElement.remove()">&times;</button>
    `;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 3000);
}

/**
 * 确认操作对话框
 * @param {string} message 确认消息
 * @param {function} callback 确认后的回调函数
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    if (!date) return '-';

    const d = new Date(date);
    if (isNaN(d.getTime())) return '-';

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

/**
 * 格式化日期时间
 * @param {string|Date} datetime 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(datetime) {
    if (!datetime) return '-';

    const d = new Date(datetime);
    if (isNaN(d.getTime())) return '-';

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
