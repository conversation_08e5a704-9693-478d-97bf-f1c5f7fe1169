<?xml version="1.0" encoding="ISO-8859-1" ?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE taglib
        PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
        "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">

<taglib>

  <tlib-version>1.0</tlib-version>
  <jsp-version>1.2</jsp-version>
  <short-name>simple</short-name>
  <uri>http://tomcat.apache.org/example-taglib</uri>
  <description>
    A simple tab library for the examples
  </description>

  <!-- A simple Tag -->
  <!-- foo tag -->
  <tag>
    <name>foo</name>
    <tag-class>examples.FooTag</tag-class>
    <tei-class>examples.FooTagExtraInfo</tei-class>
    <body-content>JSP</body-content>
    <description>
      Perform a server side action; uses 3 mandatory attributes
    </description>

    <attribute>
      <name>att1</name>
      <required>true</required>
    </attribute>
    <attribute>
      <name>att2</name>
      <required>true</required>
    </attribute>
    <attribute>
      <name>att3</name>
      <required>true</required>
    </attribute>
  </tag>

  <!-- Another simple tag -->
  <!-- log tag -->
  <tag>
    <name>log</name>
    <tag-class>examples.LogTag</tag-class>
    <body-content>TAGDEPENDENT</body-content>
    <description>
      Perform a server side action; Log the message.
    </description>
    <attribute>
      <name>toBrowser</name>
      <required>false</required>
    </attribute>
  </tag>

  <!-- Another simple Tag -->
  <!-- values tag -->
  <tag>
    <name>values</name>
    <tag-class>examples.ValuesTag</tag-class>
    <body-content>empty</body-content>
    <description>
        Accept and return values of different types. This tag is used
        to illustrate type coercions.
    </description>
    <attribute>
      <name>object</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
      <type>java.lang.Object</type>
    </attribute>
    <attribute>
      <name>string</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
      <type>java.lang.String</type>
    </attribute>
    <attribute>
      <name>long</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
      <type>long</type>
    </attribute>
    <attribute>
      <name>double</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
      <type>double</type>
    </attribute>
  </tag>
</taglib>
