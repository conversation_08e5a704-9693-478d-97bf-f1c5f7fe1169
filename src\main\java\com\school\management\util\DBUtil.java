package com.school.management.util;

import java.io.IOException;
import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

/**
 * 数据库连接工具类
 * 负责管理数据库连接的创建和释放
 */
public class DBUtil {
    
    private static String driver;
    private static String url;
    private static String username;
    private static String password;
    
    // 静态代码块，在类加载时执行，用于初始化数据库配置
    static {
        try {
            // 加载数据库配置文件
            Properties props = new Properties();
            InputStream is = DBUtil.class.getClassLoader().getResourceAsStream("db.properties");
            if (is != null) {
                props.load(is);
                driver = props.getProperty("db.driver");
                url = props.getProperty("db.url");
                username = props.getProperty("db.username");
                password = props.getProperty("db.password");
                
                // 加载数据库驱动
                Class.forName(driver);
                System.out.println("数据库驱动加载成功");
            } else {
                System.err.println("无法找到数据库配置文件 db.properties");
            }
        } catch (IOException e) {
            System.err.println("读取数据库配置文件失败: " + e.getMessage());
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            System.err.println("数据库驱动加载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取数据库连接
     * @return Connection 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection() throws SQLException {
        if (url == null || username == null || password == null) {
            throw new SQLException("数据库配置信息不完整");
        }
        return DriverManager.getConnection(url, username, password);
    }
    
    /**
     * 关闭数据库连接
     * @param conn 数据库连接对象
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭数据库连接失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 关闭数据库资源
     * @param conn 数据库连接对象
     * @param stmt 预处理语句对象
     * @param rs 结果集对象
     */
    public static void closeResources(Connection conn, PreparedStatement stmt, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                System.err.println("关闭ResultSet失败: " + e.getMessage());
            }
        }
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                System.err.println("关闭PreparedStatement失败: " + e.getMessage());
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                System.err.println("关闭Connection失败: " + e.getMessage());
            }
        }
    }

    /**
     * 关闭数据库资源（不包含ResultSet）
     * @param conn 数据库连接对象
     * @param stmt 预处理语句对象
     */
    public static void closeResources(Connection conn, PreparedStatement stmt) {
        closeResources(conn, stmt, null);
    }
    
    /**
     * 测试数据库连接
     * @return boolean 连接是否成功
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取数据库配置信息（用于调试）
     */
    public static void printConfig() {
        System.out.println("数据库配置信息:");
        System.out.println("Driver: " + driver);
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        System.out.println("Password: " + (password != null ? "***" : "null"));
    }
}
