// 发展对象模块JavaScript

// 创建模块命名空间
window.DevelopmentModule = window.DevelopmentModule || {};

// 模块内部变量
DevelopmentModule.currentPage = 1;
DevelopmentModule.pageSize = 10;
DevelopmentModule.currentSearchConditions = {};

// 加载发展对象模块内容
window.loadDevelopmentModuleContent = function() {
    const content = `
        <div class="module-header">
            <h2>发展对象管理</h2>
            <div class="module-actions">
                <button class="btn btn-primary" onclick="showAddDevelopmentForm()">
                    <i class="icon-plus"></i> 添加发展对象
                </button>
                <button class="btn btn-success" onclick="exportDevelopmentData()">
                    <i class="icon-export"></i> 导出数据
                </button>
            </div>
        </div>

        <div class="search-container">
            <div class="search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>姓名:</label>
                        <input type="text" id="searchName" placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label>院系:</label>
                        <select id="searchDepartment">
                            <option value="">请选择院系</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>专业:</label>
                        <select id="searchMajor">
                            <option value="">请选择专业</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>班级:</label>
                        <select id="searchClass">
                            <option value="">请选择班级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>状态:</label>
                        <select id="searchStatus">
                            <option value="">全部状态</option>
                            <option value="政治审查中">政治审查中</option>
                            <option value="培训中">培训中</option>
                            <option value="准备入党">准备入党</option>
                            <option value="已转预备党员">已转预备党员</option>
                            <option value="已取消">已取消</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>政治审查结果:</label>
                        <select id="searchPoliticalResult">
                            <option value="">全部结果</option>
                            <option value="通过">通过</option>
                            <option value="不通过">不通过</option>
                            <option value="待审查">待审查</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="searchDevelopments()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetDevelopmentSearch()">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table id="developmentTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>学号</th>
                        <th>院系</th>
                        <th>专业</th>
                        <th>班级</th>
                        <th>确定日期</th>
                        <th>政治审查结果</th>
                        <th>培训成绩</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="developmentTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">共 0 条记录</span>
            </div>
            <div class="pagination">
                <button class="btn btn-secondary" onclick="previousPage()" id="prevBtn">上一页</button>
                <span id="pageNumbers"></span>
                <button class="btn btn-secondary" onclick="nextPage()" id="nextBtn">下一页</button>
            </div>
        </div>

        <div class="batch-actions">
            <button class="btn btn-success" onclick="batchPoliticalReview('通过')">批量政治审查通过</button>
            <button class="btn btn-warning" onclick="batchPoliticalReview('不通过')">批量政治审查不通过</button>
            <button class="btn btn-primary" onclick="batchConvertToProbationary()">批量转预备党员</button>
            <button class="btn btn-danger" onclick="batchDeleteDevelopment()">批量删除</button>
        </div>

        <!-- 添加/编辑表单模态框 -->
        <div id="developmentModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">添加发展对象</h3>
                    <span class="close" onclick="closeDevelopmentModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="developmentForm">
                        <input type="hidden" id="developmentId">
                        <div class="form-row">
                            <div class="form-group">
                                <label>姓名 <span class="required">*</span>:</label>
                                <input type="text" id="name" required>
                            </div>
                            <div class="form-group">
                                <label>性别 <span class="required">*</span>:</label>
                                <select id="gender" required>
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>出生日期 <span class="required">*</span>:</label>
                                <input type="date" id="birthDate" required>
                            </div>
                            <div class="form-group">
                                <label>身份证号 <span class="required">*</span>:</label>
                                <input type="text" id="idCard" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>联系电话:</label>
                                <input type="text" id="phone">
                            </div>
                            <div class="form-group">
                                <label>邮箱:</label>
                                <input type="email" id="email">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>院系 <span class="required">*</span>:</label>
                                <select id="department" required onchange="loadMajorsByDepartment()">
                                    <option value="">请选择院系</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>专业 <span class="required">*</span>:</label>
                                <select id="major" required onchange="loadClassesByMajor()">
                                    <option value="">请选择专业</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>班级 <span class="required">*</span>:</label>
                                <select id="className" required>
                                    <option value="">请选择班级</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>学号 <span class="required">*</span>:</label>
                                <input type="text" id="studentId" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>确定为发展对象日期 <span class="required">*</span>:</label>
                                <input type="date" id="determinationDate" required>
                            </div>
                            <div class="form-group">
                                <label>政治审查日期:</label>
                                <input type="date" id="politicalReviewDate">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>政治审查结果:</label>
                                <select id="politicalReviewResult">
                                    <option value="待审查">待审查</option>
                                    <option value="通过">通过</option>
                                    <option value="不通过">不通过</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>培训完成日期:</label>
                                <input type="date" id="trainingCompletionDate">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>培训成绩:</label>
                                <input type="number" id="trainingScore" min="0" max="100" step="0.1" placeholder="0-100分">
                            </div>
                            <div class="form-group">
                                <label>状态:</label>
                                <select id="status">
                                    <option value="政治审查中">政治审查中</option>
                                    <option value="培训中">培训中</option>
                                    <option value="准备入党">准备入党</option>
                                    <option value="已转预备党员">已转预备党员</option>
                                    <option value="已取消">已取消</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>推荐信:</label>
                                <textarea id="recommendationLetter" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>个人陈述:</label>
                                <textarea id="personalStatement" rows="4"></textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>备注:</label>
                                <textarea id="remarks" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="saveDevelopment()">保存</button>
                    <button class="btn btn-secondary" onclick="closeDevelopmentModal()">取消</button>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('module-content').innerHTML = content;
    
    // 初始化页面
    initDevelopmentModule();
};

// 初始化模块
function initDevelopmentModule() {
    // 设置默认确定日期为今天
    document.getElementById('determinationDate').value = new Date().toISOString().split('T')[0];
    
    // 加载基础数据
    loadDepartments();
    loadDevelopmentData();
}

// 加载院系数据
function loadDepartments() {
    // 这里应该调用后端接口获取院系数据
    // 暂时使用模拟数据
    const departments = ['计算机学院', '电子信息学院', '机械工程学院', '经济管理学院', '外国语学院'];
    
    const searchDepartmentSelect = document.getElementById('searchDepartment');
    const departmentSelect = document.getElementById('department');
    
    departments.forEach(dept => {
        const option1 = new Option(dept, dept);
        const option2 = new Option(dept, dept);
        searchDepartmentSelect.add(option1);
        departmentSelect.add(option2);
    });
}

// 根据院系加载专业
function loadMajorsByDepartment() {
    const department = document.getElementById('department').value;
    const majorSelect = document.getElementById('major');
    const classSelect = document.getElementById('className');
    
    // 清空专业和班级选项
    majorSelect.innerHTML = '<option value="">请选择专业</option>';
    classSelect.innerHTML = '<option value="">请选择班级</option>';
    
    if (!department) return;
    
    // 这里应该调用后端接口获取专业数据
    // 暂时使用模拟数据
    const majors = {
        '计算机学院': ['计算机科学与技术', '软件工程', '网络工程', '信息安全'],
        '电子信息学院': ['电子信息工程', '通信工程', '自动化', '电气工程'],
        '机械工程学院': ['机械设计制造及其自动化', '材料成型及控制工程', '工业设计'],
        '经济管理学院': ['工商管理', '会计学', '市场营销', '国际经济与贸易'],
        '外国语学院': ['英语', '日语', '德语', '法语']
    };
    
    const departmentMajors = majors[department] || [];
    departmentMajors.forEach(major => {
        majorSelect.add(new Option(major, major));
    });
}

// 根据专业加载班级
function loadClassesByMajor() {
    const major = document.getElementById('major').value;
    const classSelect = document.getElementById('className');
    
    // 清空班级选项
    classSelect.innerHTML = '<option value="">请选择班级</option>';
    
    if (!major) return;
    
    // 这里应该调用后端接口获取班级数据
    // 暂时使用模拟数据
    const classes = ['2021级1班', '2021级2班', '2022级1班', '2022级2班', '2023级1班', '2023级2班'];
    
    classes.forEach(cls => {
        classSelect.add(new Option(cls, cls));
    });
}

// 加载发展对象数据
function loadDevelopmentData() {
    // 这里应该调用后端接口获取数据
    // 暂时使用模拟数据
    const mockData = {
        data: [
            {
                id: 1,
                name: '王五',
                gender: '男',
                studentId: '2020001',
                department: '计算机学院',
                major: '计算机科学与技术',
                className: '2020级1班',
                determinationDate: '2024-03-15',
                politicalReviewResult: '通过',
                trainingScore: 85.5,
                status: '培训中'
            },
            {
                id: 2,
                name: '赵六',
                gender: '女',
                studentId: '2020002',
                department: '计算机学院',
                major: '软件工程',
                className: '2020级2班',
                determinationDate: '2024-03-20',
                politicalReviewResult: '通过',
                trainingScore: 92.0,
                status: '准备入党'
            }
        ],
        total: 2,
        pageNum: 1,
        pageSize: 10,
        totalPages: 1
    };

    displayDevelopmentData(mockData);
}

// 显示发展对象数据
function displayDevelopmentData(data) {
    const tbody = document.getElementById('developmentTableBody');
    tbody.innerHTML = '';

    if (data.data && data.data.length > 0) {
        data.data.forEach(development => {
            const row = `
                <tr>
                    <td><input type="checkbox" class="row-checkbox" value="${development.id}"></td>
                    <td>${development.name}</td>
                    <td>${development.gender}</td>
                    <td>${development.studentId}</td>
                    <td>${development.department}</td>
                    <td>${development.major}</td>
                    <td>${development.className}</td>
                    <td>${formatDate(development.determinationDate)}</td>
                    <td><span class="status-badge status-${development.politicalReviewResult}">${development.politicalReviewResult}</span></td>
                    <td>${development.trainingScore || '-'}</td>
                    <td><span class="status-badge status-${development.status}">${development.status}</span></td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editDevelopment(${development.id})">编辑</button>
                        <button class="btn btn-sm btn-success" onclick="viewDevelopment(${development.id})">查看</button>
                        <button class="btn btn-sm btn-warning" onclick="updateTrainingScore(${development.id})">更新成绩</button>
                        <button class="btn btn-sm btn-danger" onclick="deleteDevelopment(${development.id})">删除</button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="12" class="text-center">暂无数据</td></tr>';
    }

    // 更新分页信息
    updatePaginationInfo(data);
}

// 更新分页信息
function updatePaginationInfo(data) {
    const paginationInfo = document.getElementById('paginationInfo');
    paginationInfo.textContent = `共 ${data.total} 条记录，第 ${data.pageNum} 页，共 ${data.totalPages} 页`;

    // 更新分页按钮状态
    document.getElementById('prevBtn').disabled = data.pageNum <= 1;
    document.getElementById('nextBtn').disabled = data.pageNum >= data.totalPages;

    // 生成页码
    generatePageNumbers(data.pageNum, data.totalPages);
}

// 生成页码
function generatePageNumbers(currentPage, totalPages) {
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
        pageBtn.textContent = i;
        pageBtn.onclick = () => goToPage(i);
        pageNumbers.appendChild(pageBtn);
    }
}

// 跳转到指定页
function goToPage(page) {
    DevelopmentModule.currentPage = page;
    loadDevelopmentData();
}

// 上一页
function previousPage() {
    if (DevelopmentModule.currentPage > 1) {
        DevelopmentModule.currentPage--;
        loadDevelopmentData();
    }
}

// 下一页
function nextPage() {
    DevelopmentModule.currentPage++;
    loadDevelopmentData();
}

// 搜索发展对象
function searchDevelopments() {
    DevelopmentModule.currentSearchConditions = {
        name: document.getElementById('searchName').value,
        department: document.getElementById('searchDepartment').value,
        major: document.getElementById('searchMajor').value,
        className: document.getElementById('searchClass').value,
        status: document.getElementById('searchStatus').value,
        politicalReviewResult: document.getElementById('searchPoliticalResult').value
    };

    DevelopmentModule.currentPage = 1;
    loadDevelopmentData();
}

// 重置搜索
function resetDevelopmentSearch() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchDepartment').value = '';
    document.getElementById('searchMajor').value = '';
    document.getElementById('searchClass').value = '';
    document.getElementById('searchStatus').value = '';
    document.getElementById('searchPoliticalResult').value = '';

    DevelopmentModule.currentSearchConditions = {};
    DevelopmentModule.currentPage = 1;
    loadDevelopmentData();
}

// 显示添加发展对象表单
function showAddDevelopmentForm() {
    document.getElementById('modalTitle').textContent = '添加发展对象';
    document.getElementById('developmentForm').reset();
    document.getElementById('developmentId').value = '';
    document.getElementById('determinationDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('developmentModal').style.display = 'block';
}

// 编辑发展对象
function editDevelopment(id) {
    // 这里应该调用后端接口获取发展对象详细信息
    // 暂时使用模拟数据
    const mockDevelopment = {
        id: id,
        name: '王五',
        gender: '男',
        birthDate: '1999-05-15',
        idCard: '123456789012345679',
        phone: '13800138001',
        email: '<EMAIL>',
        department: '计算机学院',
        major: '计算机科学与技术',
        className: '2020级1班',
        studentId: '2020001',
        determinationDate: '2024-03-15',
        politicalReviewDate: '2024-04-01',
        politicalReviewResult: '通过',
        trainingCompletionDate: '2024-05-01',
        trainingScore: 85.5,
        recommendationLetter: '推荐信内容',
        personalStatement: '个人陈述内容',
        status: '培训中',
        remarks: '备注信息'
    };

    // 填充表单
    fillDevelopmentForm(mockDevelopment);

    document.getElementById('modalTitle').textContent = '编辑发展对象';
    document.getElementById('developmentModal').style.display = 'block';
}

// 填充发展对象表单
function fillDevelopmentForm(development) {
    document.getElementById('developmentId').value = development.id || '';
    document.getElementById('name').value = development.name || '';
    document.getElementById('gender').value = development.gender || '';
    document.getElementById('birthDate').value = development.birthDate || '';
    document.getElementById('idCard').value = development.idCard || '';
    document.getElementById('phone').value = development.phone || '';
    document.getElementById('email').value = development.email || '';
    document.getElementById('department').value = development.department || '';
    document.getElementById('major').value = development.major || '';
    document.getElementById('className').value = development.className || '';
    document.getElementById('studentId').value = development.studentId || '';
    document.getElementById('determinationDate').value = development.determinationDate || '';
    document.getElementById('politicalReviewDate').value = development.politicalReviewDate || '';
    document.getElementById('politicalReviewResult').value = development.politicalReviewResult || '待审查';
    document.getElementById('trainingCompletionDate').value = development.trainingCompletionDate || '';
    document.getElementById('trainingScore').value = development.trainingScore || '';
    document.getElementById('recommendationLetter').value = development.recommendationLetter || '';
    document.getElementById('personalStatement').value = development.personalStatement || '';
    document.getElementById('status').value = development.status || '政治审查中';
    document.getElementById('remarks').value = development.remarks || '';
}

// 查看发展对象详情
function viewDevelopment(id) {
    // 这里应该调用后端接口获取发展对象详细信息
    // 然后显示在只读模式的模态框中
    editDevelopment(id);

    // 将表单设置为只读
    const formElements = document.querySelectorAll('#developmentForm input, #developmentForm select, #developmentForm textarea');
    formElements.forEach(element => {
        element.disabled = true;
    });

    document.getElementById('modalTitle').textContent = '查看发展对象详情';
    document.querySelector('.modal-footer').innerHTML = '<button class="btn btn-secondary" onclick="closeDevelopmentModal()">关闭</button>';
}

// 保存发展对象
function saveDevelopment() {
    const form = document.getElementById('developmentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const developmentData = {
        id: document.getElementById('developmentId').value,
        name: document.getElementById('name').value,
        gender: document.getElementById('gender').value,
        birthDate: document.getElementById('birthDate').value,
        idCard: document.getElementById('idCard').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        department: document.getElementById('department').value,
        major: document.getElementById('major').value,
        className: document.getElementById('className').value,
        studentId: document.getElementById('studentId').value,
        determinationDate: document.getElementById('determinationDate').value,
        politicalReviewDate: document.getElementById('politicalReviewDate').value,
        politicalReviewResult: document.getElementById('politicalReviewResult').value,
        trainingCompletionDate: document.getElementById('trainingCompletionDate').value,
        trainingScore: document.getElementById('trainingScore').value,
        recommendationLetter: document.getElementById('recommendationLetter').value,
        personalStatement: document.getElementById('personalStatement').value,
        status: document.getElementById('status').value,
        remarks: document.getElementById('remarks').value
    };

    // 这里应该调用后端接口保存数据
    console.log('保存发展对象数据:', developmentData);

    // 模拟保存成功
    showMessage('保存成功', 'success');
    closeDevelopmentModal();
    loadDevelopmentData();
}

// 关闭发展对象模态框
function closeDevelopmentModal() {
    document.getElementById('developmentModal').style.display = 'none';

    // 恢复表单状态
    const formElements = document.querySelectorAll('#developmentForm input, #developmentForm select, #developmentForm textarea');
    formElements.forEach(element => {
        element.disabled = false;
    });

    // 恢复模态框底部按钮
    document.querySelector('.modal-footer').innerHTML = `
        <button class="btn btn-primary" onclick="saveDevelopment()">保存</button>
        <button class="btn btn-secondary" onclick="closeDevelopmentModal()">取消</button>
    `;
}

// 删除发展对象
function deleteDevelopment(id) {
    confirmAction('确定要删除这个发展对象吗？', () => {
        // 这里应该调用后端接口删除数据
        console.log('删除发展对象:', id);

        // 模拟删除成功
        showMessage('删除成功', 'success');
        loadDevelopmentData();
    });
}

// 更新培训成绩
function updateTrainingScore(id) {
    const score = prompt('请输入培训成绩（0-100分）:');
    if (score !== null && score !== '') {
        const scoreNum = parseFloat(score);
        if (isNaN(scoreNum) || scoreNum < 0 || scoreNum > 100) {
            showMessage('请输入有效的成绩（0-100分）', 'error');
            return;
        }

        // 这里应该调用后端接口更新成绩
        console.log('更新培训成绩:', id, scoreNum);

        // 模拟更新成功
        showMessage('培训成绩更新成功', 'success');
        loadDevelopmentData();
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 获取选中的发展对象ID
function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// 批量政治审查
function batchPoliticalReview(result) {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showMessage('请选择要审查的发展对象', 'warning');
        return;
    }

    const action = result === '通过' ? '通过' : '不通过';
    confirmAction(`确定要批量政治审查${action}选中的发展对象吗？`, () => {
        // 这里应该调用后端接口批量审查
        console.log(`批量政治审查${action}:`, selectedIds);

        // 模拟操作成功
        showMessage(`批量政治审查${action}成功`, 'success');
        loadDevelopmentData();

        // 取消全选
        document.getElementById('selectAll').checked = false;
    });
}

// 批量转为预备党员
function batchConvertToProbationary() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showMessage('请选择要转为预备党员的发展对象', 'warning');
        return;
    }

    confirmAction('确定要批量转为预备党员吗？', () => {
        // 这里应该调用后端接口批量转换
        console.log('批量转为预备党员:', selectedIds);

        // 模拟操作成功
        showMessage('批量转为预备党员成功', 'success');
        loadDevelopmentData();

        // 取消全选
        document.getElementById('selectAll').checked = false;
    });
}

// 批量删除
function batchDeleteDevelopment() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showMessage('请选择要删除的发展对象', 'warning');
        return;
    }

    confirmAction('确定要批量删除选中的发展对象吗？', () => {
        // 这里应该调用后端接口批量删除
        console.log('批量删除发展对象:', selectedIds);

        // 模拟删除成功
        showMessage('批量删除成功', 'success');
        loadDevelopmentData();

        // 取消全选
        document.getElementById('selectAll').checked = false;
    });
}

// 导出数据
function exportDevelopmentData() {
    // 这里应该调用后端接口导出数据
    console.log('导出发展对象数据');
    showMessage('导出功能开发中...', 'info');
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modal = document.getElementById('developmentModal');
    if (modal && event.target === modal) {
        closeDevelopmentModal();
    }
});
