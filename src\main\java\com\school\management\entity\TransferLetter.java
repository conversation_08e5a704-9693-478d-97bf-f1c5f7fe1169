package com.school.management.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 组织关系介绍信实体类
 */
public class TransferLetter {
    
    private Integer id;                    // 主键ID
    private Integer memberId;              // 党员ID
    private String memberName;             // 党员姓名
    private String idCard;                 // 身份证号
    private String originalBranch;         // 原党支部
    private String targetBranch;           // 目标党支部
    private String transferReason;         // 转移原因
    private Date issueDate;                // 开具日期
    private Date validUntil;               // 有效期至
    private String letterNumber;           // 介绍信编号
    private String issuerName;             // 开具人姓名
    private String issuerPosition;         // 开具人职务
    private String contactPhone;           // 联系电话
    private String status;                 // 状态
    private Date receiveDate;              // 接收日期
    private String receiveBranch;          // 接收党支部
    private String remarks;                // 备注
    private Timestamp createTime;          // 创建时间
    private Timestamp updateTime;          // 更新时间
    
    // 无参构造函数
    public TransferLetter() {}
    
    // 全参构造函数
    public TransferLetter(Integer id, Integer memberId, String memberName, String idCard,
                         String originalBranch, String targetBranch, String transferReason,
                         Date issueDate, Date validUntil, String letterNumber, String issuerName,
                         String issuerPosition, String contactPhone, String status, Date receiveDate,
                         String receiveBranch, String remarks, Timestamp createTime, Timestamp updateTime) {
        this.id = id;
        this.memberId = memberId;
        this.memberName = memberName;
        this.idCard = idCard;
        this.originalBranch = originalBranch;
        this.targetBranch = targetBranch;
        this.transferReason = transferReason;
        this.issueDate = issueDate;
        this.validUntil = validUntil;
        this.letterNumber = letterNumber;
        this.issuerName = issuerName;
        this.issuerPosition = issuerPosition;
        this.contactPhone = contactPhone;
        this.status = status;
        this.receiveDate = receiveDate;
        this.receiveBranch = receiveBranch;
        this.remarks = remarks;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
    
    // Getter和Setter方法
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public Integer getMemberId() { return memberId; }
    public void setMemberId(Integer memberId) { this.memberId = memberId; }
    
    public String getMemberName() { return memberName; }
    public void setMemberName(String memberName) { this.memberName = memberName; }
    
    public String getIdCard() { return idCard; }
    public void setIdCard(String idCard) { this.idCard = idCard; }
    
    public String getOriginalBranch() { return originalBranch; }
    public void setOriginalBranch(String originalBranch) { this.originalBranch = originalBranch; }
    
    public String getTargetBranch() { return targetBranch; }
    public void setTargetBranch(String targetBranch) { this.targetBranch = targetBranch; }
    
    public String getTransferReason() { return transferReason; }
    public void setTransferReason(String transferReason) { this.transferReason = transferReason; }
    
    public Date getIssueDate() { return issueDate; }
    public void setIssueDate(Date issueDate) { this.issueDate = issueDate; }
    
    public Date getValidUntil() { return validUntil; }
    public void setValidUntil(Date validUntil) { this.validUntil = validUntil; }
    
    public String getLetterNumber() { return letterNumber; }
    public void setLetterNumber(String letterNumber) { this.letterNumber = letterNumber; }
    
    public String getIssuerName() { return issuerName; }
    public void setIssuerName(String issuerName) { this.issuerName = issuerName; }
    
    public String getIssuerPosition() { return issuerPosition; }
    public void setIssuerPosition(String issuerPosition) { this.issuerPosition = issuerPosition; }
    
    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Date getReceiveDate() { return receiveDate; }
    public void setReceiveDate(Date receiveDate) { this.receiveDate = receiveDate; }
    
    public String getReceiveBranch() { return receiveBranch; }
    public void setReceiveBranch(String receiveBranch) { this.receiveBranch = receiveBranch; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Timestamp getCreateTime() { return createTime; }
    public void setCreateTime(Timestamp createTime) { this.createTime = createTime; }
    
    public Timestamp getUpdateTime() { return updateTime; }
    public void setUpdateTime(Timestamp updateTime) { this.updateTime = updateTime; }
    
    @Override
    public String toString() {
        return "TransferLetter{" +
                "id=" + id +
                ", memberId=" + memberId +
                ", memberName='" + memberName + '\'' +
                ", idCard='" + idCard + '\'' +
                ", originalBranch='" + originalBranch + '\'' +
                ", targetBranch='" + targetBranch + '\'' +
                ", transferReason='" + transferReason + '\'' +
                ", issueDate=" + issueDate +
                ", validUntil=" + validUntil +
                ", letterNumber='" + letterNumber + '\'' +
                ", issuerName='" + issuerName + '\'' +
                ", issuerPosition='" + issuerPosition + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", status='" + status + '\'' +
                ", receiveDate=" + receiveDate +
                ", receiveBranch='" + receiveBranch + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
