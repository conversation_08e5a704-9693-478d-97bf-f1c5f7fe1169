# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do not edit this file directly.
# To edit translations see: https://tomcat.apache.org/getinvolved.html#Translations

cookies.cookies=Tu navegador est\u00e1 enviando los siguientes cookies:
cookies.make-cookie=Crea un cookie para enviarlo a tu navegador
cookies.name=Nombre:
cookies.no-cookies=Tu navegador no est\u00e1 enviando cookies
cookies.set=Acabas de enviar a tu navegador estos cookies:
cookies.title=Ejemplo de Cookies
cookies.value=Valor:

helloworld.title=Hola Mundo!

requestheader.title=Ejemplo de Cabecera de Requerimiento:

requestinfo.label.method=M\u00e9todo:
requestinfo.label.pathinfo=Info de Ruta:
requestinfo.label.protocol=Protocolo:
requestinfo.label.remoteaddr=Direccion Remota:
requestinfo.label.requesturi=URI de Requerimiento:
requestinfo.title=Ejemplo de Informacion de Requerimiento:

requestparams.firstname=Nombre:
requestparams.lastname=Apellidos:
requestparams.no-params=No hay p\u00e1rametro. Por favor, usa alguno
requestparams.params-in-req=Par\u00e1metros en este Request:
requestparams.title=Ejemplo de solicitud con par\u00e1metros:

sessions.adddata=A\u00f1ade datos a tu sesi\u00f3n:
sessions.created=Creado:
sessions.data=Lo siguientes datos est\u00e1n en tu sesi\u00f3n:
sessions.dataname=Nombre del atributo de sesi\u00f3n:
sessions.datavalue=Valor del atributo de sesi\u00f3n:
sessions.id=ID de Sesi\u00f3n:
sessions.lastaccessed=Ultimo Acceso:
sessions.title=Ejemplo de Sesiones
