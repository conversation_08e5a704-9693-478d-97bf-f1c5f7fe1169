<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入党申请人管理 - 学校党员信息管理系统</title>
    <link rel="stylesheet" href="<%=request.getContextPath()%>/css/style.css">
    <style>
        .management-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .btn-group {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .search-form input, .search-form select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .data-table tr:hover {
            background-color: #e8f4f8;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover { color: black; }
        
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-待审核 { background: #fff3cd; color: #856404; }
        .status-已通过 { background: #d4edda; color: #155724; }
        .status-已拒绝 { background: #f8d7da; color: #721c24; }
        .status-已转积极分子 { background: #d1ecf1; color: #0c5460; }
        
        .auto-fill-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover { background: #f8f9fa; }
        .pagination button:disabled { opacity: 0.5; cursor: not-allowed; }
        .pagination .current { background: #007bff; color: white; }
    </style>
</head>
<body>
    <div class="management-container">
        <h1>入党申请人管理</h1>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="btn-group">
                <button class="btn btn-primary" onclick="showAddModal()">
                    <i class="icon">+</i> 新增申请人
                </button>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="icon">↻</i> 刷新
                </button>
                <button class="btn btn-success" onclick="exportData()">
                    <i class="icon">↓</i> 导出
                </button>
            </div>
            
            <!-- 搜索表单 -->
            <div class="search-form">
                <input type="text" id="searchName" placeholder="姓名" />
                <input type="text" id="searchIdCard" placeholder="身份证号" />
                <select id="searchGrade">
                    <option value="">全部年级</option>
                    <option value="2021级">2021级</option>
                    <option value="2022级">2022级</option>
                    <option value="2023级">2023级</option>
                    <option value="2024级">2024级</option>
                </select>
                <select id="searchStatus">
                    <option value="">全部状态</option>
                    <option value="待审核">待审核</option>
                    <option value="已通过">已通过</option>
                    <option value="已拒绝">已拒绝</option>
                    <option value="已转积极分子">已转积极分子</option>
                </select>
                <button class="btn btn-info" onclick="searchApplicants()">搜索</button>
                <button class="btn btn-warning" onclick="resetSearch()">重置</button>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <table class="data-table" id="applicantTable">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>年龄</th>
                    <th>身份证号</th>
                    <th>年级</th>
                    <th>共青团员</th>
                    <th>申请日期</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="applicantTableBody">
                <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
        
        <!-- 分页 -->
        <div class="pagination" id="pagination">
            <!-- 分页控件将通过JavaScript动态生成 -->
        </div>
        
        <!-- 返回主页 -->
        <div style="margin-top: 20px; text-align: center;">
            <a href="<%=request.getContextPath()%>/index.jsp" class="btn btn-info">返回主页</a>
        </div>
    </div>

    <!-- 新增/编辑申请人模态框 -->
    <div id="applicantModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增入党申请人</h3>
                <span class="close" onclick="closeApplicantModal()">&times;</span>
            </div>
            <form id="applicantForm" onsubmit="return saveApplicant(event)">
                <input type="hidden" id="applicantId" />
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="applicantName">姓名 *</label>
                        <input type="text" id="applicantName" name="name" required />
                    </div>
                    <div class="form-group">
                        <label for="applicantIdCard">身份证号 *</label>
                        <input type="text" id="applicantIdCard" name="idCard" required 
                               pattern="[0-9]{17}[0-9Xx]" title="请输入18位身份证号"
                               onblur="parseIdCardInfo()" />
                    </div>
                </div>
                
                <div id="autoFillInfo" class="auto-fill-info" style="display: none;">
                    <strong>自动识别信息：</strong>
                    <span id="autoGender"></span> | 
                    <span id="autoBirthDate"></span> | 
                    <span id="autoAge"></span>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="applicantNativePlace">户籍地</label>
                        <input type="text" id="applicantNativePlace" name="nativePlace" />
                    </div>
                    <div class="form-group">
                        <label for="applicantGrade">年级 *</label>
                        <select id="applicantGrade" name="grade" required>
                            <option value="">请选择年级</option>
                            <option value="2021级">2021级</option>
                            <option value="2022级">2022级</option>
                            <option value="2023级">2023级</option>
                            <option value="2024级">2024级</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="applicantAddress">地址</label>
                    <input type="text" id="applicantAddress" name="address" />
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="applicantPhone">联系电话</label>
                        <input type="tel" id="applicantPhone" name="phone" />
                    </div>
                    <div class="form-group">
                        <label for="applicantIsLeagueMember">是否为共青团员</label>
                        <select id="applicantIsLeagueMember" name="isLeagueMember">
                            <option value="false">否</option>
                            <option value="true">是</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="applicantApplicationDate">申请日期</label>
                    <input type="date" id="applicantApplicationDate" name="applicationDate" />
                </div>
                
                <div class="form-group">
                    <label for="applicantRemarks">备注</label>
                    <textarea id="applicantRemarks" name="remarks" placeholder="请输入备注信息"></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-warning" onclick="closeApplicantModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 积极分子审议模态框 -->
    <div id="reviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>积极分子审议</h3>
                <span class="close" onclick="closeReviewModal()">&times;</span>
            </div>
            <div id="reviewApplicantInfo" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <!-- 申请人信息将在这里显示 -->
            </div>
            <form id="reviewForm" onsubmit="return submitReview(event)">
                <input type="hidden" id="reviewApplicantId" />
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="activistDate">确认入党积极分子时间 *</label>
                        <input type="date" id="activistDate" name="activistDate" required />
                    </div>
                    <div class="form-group">
                        <label for="branchSecretary">支部书记 *</label>
                        <input type="text" id="branchSecretary" name="branchSecretary" required />
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="hasLeagueRecommendation">是否经过共青团推优</label>
                    <select id="hasLeagueRecommendation" name="hasLeagueRecommendation">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="reviewRemarks">审议备注</label>
                    <textarea id="reviewRemarks" name="remarks" placeholder="请输入审议备注"></textarea>
                </div>
                
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-warning" onclick="closeReviewModal()">取消</button>
                    <button type="submit" class="btn btn-success">确定转为积极分子</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 设置全局上下文路径
        window.contextPath = '<%=request.getContextPath()%>';
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('入党申请人管理页面加载完成');
            loadApplicantData();
            
            // 设置默认申请日期为今天
            document.getElementById('applicantApplicationDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('activistDate').value = new Date().toISOString().split('T')[0];
        });
    </script>
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let currentSearchConditions = {};

        // 加载申请人数据
        function loadApplicantData() {
            console.log('加载申请人数据...');

            // 构建请求参数
            const params = new URLSearchParams({
                page: currentPage,
                size: pageSize,
                ...currentSearchConditions
            });

            // 发送请求到后端API
            fetch(`${window.contextPath}/api/applicant/list?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayApplicantData(data.data);
                        updatePagination(data.pagination);
                    } else {
                        console.error('加载数据失败:', data.message);
                        showMessage('加载数据失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    // 显示模拟数据用于演示
                    displayMockData();
                });
        }

        // 显示模拟数据
        function displayMockData() {
            const mockData = [
                {
                    id: 1,
                    name: '张三',
                    gender: '男',
                    age: 20,
                    idCard: '110101200401011234',
                    grade: '2022级',
                    isLeagueMember: true,
                    applicationDate: '2024-01-15',
                    status: '待审核'
                },
                {
                    id: 2,
                    name: '李四',
                    gender: '女',
                    age: 19,
                    idCard: '110101200501015678',
                    grade: '2023级',
                    isLeagueMember: true,
                    applicationDate: '2024-02-20',
                    status: '已通过'
                },
                {
                    id: 3,
                    name: '王五',
                    gender: '男',
                    age: 21,
                    idCard: '110101200301019012',
                    grade: '2021级',
                    isLeagueMember: false,
                    applicationDate: '2024-03-10',
                    status: '已转积极分子'
                }
            ];

            displayApplicantData(mockData);
            updatePagination({ currentPage: 1, totalPages: 1, totalCount: 3 });
        }

        // 显示申请人数据
        function displayApplicantData(data) {
            const tbody = document.getElementById('applicantTableBody');
            tbody.innerHTML = '';

            if (!data || data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center; color: #999;">暂无数据</td></tr>';
                return;
            }

            data.forEach((applicant, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${(currentPage - 1) * pageSize + index + 1}</td>
                    <td>${applicant.name || '-'}</td>
                    <td>${applicant.gender || '-'}</td>
                    <td>${applicant.age || '-'}</td>
                    <td>${applicant.idCard || '-'}</td>
                    <td>${applicant.grade || '-'}</td>
                    <td>${applicant.isLeagueMember ? '是' : '否'}</td>
                    <td>${applicant.applicationDate || '-'}</td>
                    <td><span class="status-badge status-${applicant.status}">${applicant.status}</span></td>
                    <td>
                        <button class="btn btn-info" onclick="editApplicant(${applicant.id})" style="margin-right: 5px;">编辑</button>
                        ${applicant.status === '已通过' ?
                            `<button class="btn btn-success" onclick="showReviewModal(${applicant.id})">积极分子审议</button>` :
                            ''}
                        <button class="btn btn-danger" onclick="deleteApplicant(${applicant.id})" style="margin-left: 5px;">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新分页
        function updatePagination(pagination) {
            if (!pagination) return;

            totalPages = pagination.totalPages || 1;
            const paginationDiv = document.getElementById('pagination');
            paginationDiv.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage <= 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    loadApplicantData();
                }
            };
            paginationDiv.appendChild(prevBtn);

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'current' : '';
                pageBtn.onclick = () => {
                    currentPage = i;
                    loadApplicantData();
                };
                paginationDiv.appendChild(pageBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadApplicantData();
                }
            };
            paginationDiv.appendChild(nextBtn);

            // 显示统计信息
            const info = document.createElement('span');
            info.textContent = `第 ${currentPage} 页，共 ${totalPages} 页，共 ${pagination.totalCount || 0} 条记录`;
            info.style.marginLeft = '20px';
            paginationDiv.appendChild(info);
        }

        // 搜索申请人
        function searchApplicants() {
            currentSearchConditions = {
                name: document.getElementById('searchName').value.trim(),
                idCard: document.getElementById('searchIdCard').value.trim(),
                grade: document.getElementById('searchGrade').value,
                status: document.getElementById('searchStatus').value
            };

            // 移除空值
            Object.keys(currentSearchConditions).forEach(key => {
                if (!currentSearchConditions[key]) {
                    delete currentSearchConditions[key];
                }
            });

            currentPage = 1;
            loadApplicantData();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchName').value = '';
            document.getElementById('searchIdCard').value = '';
            document.getElementById('searchGrade').value = '';
            document.getElementById('searchStatus').value = '';

            currentSearchConditions = {};
            currentPage = 1;
            loadApplicantData();
        }

        // 刷新数据
        function refreshData() {
            loadApplicantData();
            showMessage('数据已刷新', 'success');
        }

        // 导出数据
        function exportData() {
            showMessage('导出功能开发中...', 'info');
        }

        // 显示新增模态框
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增入党申请人';
            document.getElementById('applicantForm').reset();
            document.getElementById('applicantId').value = '';
            document.getElementById('autoFillInfo').style.display = 'none';

            // 设置默认值
            document.getElementById('applicantApplicationDate').value = new Date().toISOString().split('T')[0];

            document.getElementById('applicantModal').style.display = 'block';
        }

        // 编辑申请人
        function editApplicant(id) {
            console.log('编辑申请人:', id);
            // 这里应该从后端获取申请人详细信息
            showMessage('编辑功能开发中...', 'info');
        }

        // 删除申请人
        function deleteApplicant(id) {
            if (confirm('确定要删除这个申请人吗？')) {
                console.log('删除申请人:', id);
                showMessage('删除功能开发中...', 'info');
            }
        }

        // 关闭申请人模态框
        function closeApplicantModal() {
            document.getElementById('applicantModal').style.display = 'none';
        }

        // 保存申请人
        function saveApplicant(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const applicantData = Object.fromEntries(formData.entries());

            console.log('保存申请人数据:', applicantData);

            // 这里应该发送到后端API
            showMessage('申请人信息保存成功！', 'success');
            closeApplicantModal();
            loadApplicantData();

            return false;
        }

        // 解析身份证信息
        function parseIdCardInfo() {
            const idCard = document.getElementById('applicantIdCard').value.trim();
            const infoDiv = document.getElementById('autoFillInfo');

            if (idCard.length === 18) {
                try {
                    // 解析性别
                    const genderCode = parseInt(idCard.substring(16, 17));
                    const gender = genderCode % 2 === 1 ? '男' : '女';

                    // 解析出生日期
                    const birthStr = idCard.substring(6, 14);
                    const year = parseInt(birthStr.substring(0, 4));
                    const month = parseInt(birthStr.substring(4, 6));
                    const day = parseInt(birthStr.substring(6, 8));
                    const birthDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

                    // 计算年龄
                    const today = new Date();
                    const birth = new Date(year, month - 1, day);
                    let age = today.getFullYear() - birth.getFullYear();
                    if (today.getMonth() < birth.getMonth() ||
                        (today.getMonth() === birth.getMonth() && today.getDate() < birth.getDate())) {
                        age--;
                    }

                    // 显示解析结果
                    document.getElementById('autoGender').textContent = `性别: ${gender}`;
                    document.getElementById('autoBirthDate').textContent = `出生日期: ${birthDate}`;
                    document.getElementById('autoAge').textContent = `年龄: ${age}岁`;

                    infoDiv.style.display = 'block';

                } catch (error) {
                    console.error('解析身份证信息失败:', error);
                    infoDiv.style.display = 'none';
                }
            } else {
                infoDiv.style.display = 'none';
            }
        }

        // 显示积极分子审议模态框
        function showReviewModal(applicantId) {
            console.log('显示积极分子审议模态框:', applicantId);

            // 设置申请人ID
            document.getElementById('reviewApplicantId').value = applicantId;

            // 获取申请人信息并显示
            // 这里应该从后端获取申请人详细信息
            const mockApplicantInfo = {
                name: '张三',
                gender: '男',
                age: 20,
                idCard: '110101200401011234',
                grade: '2022级',
                isLeagueMember: true,
                applicationDate: '2024-01-15'
            };

            const infoDiv = document.getElementById('reviewApplicantInfo');
            infoDiv.innerHTML = `
                <h4>申请人信息</h4>
                <p><strong>姓名:</strong> ${mockApplicantInfo.name}</p>
                <p><strong>性别:</strong> ${mockApplicantInfo.gender} | <strong>年龄:</strong> ${mockApplicantInfo.age}岁</p>
                <p><strong>身份证号:</strong> ${mockApplicantInfo.idCard}</p>
                <p><strong>年级:</strong> ${mockApplicantInfo.grade} | <strong>共青团员:</strong> ${mockApplicantInfo.isLeagueMember ? '是' : '否'}</p>
                <p><strong>申请日期:</strong> ${mockApplicantInfo.applicationDate}</p>
            `;

            // 重置表单
            document.getElementById('reviewForm').reset();
            document.getElementById('activistDate').value = new Date().toISOString().split('T')[0];

            // 显示模态框
            document.getElementById('reviewModal').style.display = 'block';
        }

        // 关闭审议模态框
        function closeReviewModal() {
            document.getElementById('reviewModal').style.display = 'none';
        }

        // 提交审议
        function submitReview(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const reviewData = Object.fromEntries(formData.entries());
            reviewData.applicantId = document.getElementById('reviewApplicantId').value;

            console.log('提交积极分子审议:', reviewData);

            if (confirm('确定将该申请人转为入党积极分子吗？')) {
                // 这里应该发送到后端API
                showMessage('积极分子审议成功，已转为入党积极分子！', 'success');
                closeReviewModal();
                loadApplicantData();
            }

            return false;
        }

        // 显示消息提示
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.innerHTML = `
                <span>${message}</span>
                <button class="message-close" onclick="this.parentElement.remove()">&times;</button>
            `;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                if (messageDiv.parentElement) {
                    messageDiv.remove();
                }
            }, 3000);
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const applicantModal = document.getElementById('applicantModal');
            const reviewModal = document.getElementById('reviewModal');

            if (event.target === applicantModal) {
                closeApplicantModal();
            }
            if (event.target === reviewModal) {
                closeReviewModal();
            }
        });
    </script>
</body>
</html>
