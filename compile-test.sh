#!/bin/bash

echo "========================================"
echo "Java编译测试脚本"
echo "========================================"

# 创建编译输出目录
mkdir -p build/classes

echo
echo "1. 编译实体类..."
javac -d build/classes src/main/java/com/school/management/entity/*.java
if [ $? -ne 0 ]; then
    echo "[错误] 实体类编译失败"
    exit 1
else
    echo "[成功] 实体类编译完成"
fi

echo
echo "2. 编译DAO接口..."
javac -cp build/classes -d build/classes src/main/java/com/school/management/dao/*.java
if [ $? -ne 0 ]; then
    echo "[错误] DAO接口编译失败"
    exit 1
else
    echo "[成功] DAO接口编译完成"
fi

echo
echo "3. 编译Service接口..."
javac -cp build/classes -d build/classes src/main/java/com/school/management/service/*.java
if [ $? -ne 0 ]; then
    echo "[错误] Service接口编译失败"
    exit 1
else
    echo "[成功] Service接口编译完成"
fi

echo
echo "4. 编译工具类（跳过需要外部依赖的类）..."
javac -cp build/classes -d build/classes src/main/java/com/school/management/util/JsonUtil.java
if [ $? -ne 0 ]; then
    echo "[警告] JsonUtil编译失败，可能需要外部依赖"
else
    echo "[成功] JsonUtil编译完成"
fi

echo
echo "5. 编译DAO实现类..."
javac -cp build/classes -d build/classes src/main/java/com/school/management/dao/impl/*.java
if [ $? -ne 0 ]; then
    echo "[错误] DAO实现类编译失败"
    exit 1
else
    echo "[成功] DAO实现类编译完成"
fi

echo
echo "6. 编译Service实现类..."
javac -cp build/classes -d build/classes src/main/java/com/school/management/service/impl/*.java
if [ $? -ne 0 ]; then
    echo "[错误] Service实现类编译失败"
    exit 1
else
    echo "[成功] Service实现类编译完成"
fi

echo
echo "========================================"
echo "编译测试完成！"
echo "========================================"
echo
echo "核心业务代码编译成功！"
echo
echo "注意：以下类需要外部依赖，编译时会报错（这是正常现象）："
echo "- PartyApplicantServlet.java （需要Servlet API）"
echo "- CharacterEncodingFilter.java （需要Servlet API）"
echo "- DBUtil.java （需要MySQL JDBC驱动）"
echo
echo "解决方案："
echo "1. 下载 mysql-connector-java-8.0.x.jar"
echo "2. 下载 servlet-api.jar 或部署到Tomcat"
echo "3. 将jar包添加到classpath"
echo
