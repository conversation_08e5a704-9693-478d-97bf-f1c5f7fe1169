# 学校党员信息管理系统

## 项目简介

这是一个基于JavaWeb技术栈开发的学校党员信息管理系统，主要用于管理学校党员发展的各个阶段，包括入党申请人、入党积极分子、发展对象、预备党员、正式党员以及组织关系介绍信的管理。

## 技术栈

- **前端**: JSP + HTML5 + CSS3 + JavaScript (原生)
- **后端**: Java + Servlet + JDBC
- **数据库**: MySQL 8.0+
- **开发工具**: IntelliJ IDEA
- **Web服务器**: Tomcat 9.0+
- **页面技术**: JSP (Java Server Pages) 支持动态内容生成

## 功能模块

### 已完成功能

1. **入党申请人管理** ✅
   - 申请人信息的增删改查
   - 多条件搜索和分页
   - 批量审核和删除
   - 数据导出功能

2. **基础架构** ✅
   - 数据库连接工具类
   - JSON处理工具类
   - 字符编码过滤器
   - 分页查询支持
   - 响应式前端界面
   - JSP动态页面支持
   - 服务器端渲染
   - 会话状态管理
   - 实时数据显示

3. **入党申请人模块** ✅
   - 申请人信息录入（支持身份证自动识别）
   - 申请审核流程
   - 状态跟踪管理
   - 积极分子审议功能
   - 信息自动传递到积极分子阶段

### 待开发功能

4. **入党积极分子管理** 🔄
5. **发展对象管理** ⏳
6. **预备党员管理** ⏳
7. **正式党员管理** ⏳
8. **组织关系介绍信管理** ⏳

## 项目结构

```
Management/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/school/management/
│   │   │       ├── entity/          # 实体类
│   │   │       ├── dao/             # 数据访问层
│   │   │       │   └── impl/
│   │   │       ├── service/         # 业务逻辑层
│   │   │       │   └── impl/
│   │   │       ├── servlet/         # Web控制层
│   │   │       └── util/            # 工具类
│   │   ├── resources/               # 配置文件
│   │   │   ├── db.properties        # 数据库配置
│   │   │   └── database.sql         # 数据库脚本
│   │   └── webapp/                  # Web资源
│   │       ├── WEB-INF/
│   │       │   └── web.xml          # Web配置
│   │       ├── css/                 # 样式文件
│   │       ├── js/                  # JavaScript文件
│   │       ├── pages/               # 页面文件
│   │       ├── index.html           # 主页面
│   │       └── test.html            # 测试页面
├── README.md                        # 项目说明
└── pom.xml                          # Maven配置(如需要)
```

## 数据库设计

系统包含以下主要数据表：

1. `party_applicant` - 入党申请人表
2. `party_activist` - 入党积极分子表
3. `development_target` - 发展对象表
4. `probationary_member` - 预备党员表
5. `formal_member` - 正式党员表
6. `transfer_letter` - 组织关系介绍信表

详细的表结构请参考 `src/main/resources/database.sql` 文件。

## 安装和部署

### 环境要求

- JDK 8+
- MySQL 8.0+
- Tomcat 9.0+
- IntelliJ IDEA (推荐)

### 部署步骤

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd Management
   ```

2. **数据库配置**
   - 安装MySQL数据库
   - 创建数据库用户和数据库
   - 执行 `src/main/resources/database.sql` 创建表结构
   - 修改 `src/main/resources/db.properties` 中的数据库连接信息

3. **添加依赖**
   - 下载MySQL JDBC驱动 (mysql-connector-java-8.0.x.jar)
   - 将jar包添加到项目的classpath中

4. **配置Web服务器**
   - 安装Tomcat服务器
   - 将项目部署到Tomcat的webapps目录
   - 启动Tomcat服务器

5. **访问系统**
   - 主页面(JSP): `http://localhost:8080/Management/index.jsp`
   - 入党申请人管理: `http://localhost:8080/Management/applicant-management.jsp`
   - 功能测试: `http://localhost:8080/Management/test.jsp`
   - 数据库测试: `http://localhost:8080/Management/db-test.jsp`
   - Java错误检查: `http://localhost:8080/Management/java-error-check.jsp`
   - Bug修复验证: `http://localhost:8080/Management/test-fixes.jsp`

   **注意**: 现在优先使用JSP页面，支持动态内容和服务器端渲染

### 数据库配置

**当前配置信息：**
- 数据库地址：`**************************************`
- 数据库名：`management`
- 用户名：`root`
- 密码：`123456`

**部署步骤：**

1. 启动MySQL服务
2. 创建数据库：
```sql
CREATE DATABASE IF NOT EXISTS management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 执行数据库脚本：
```bash
mysql -u root -p management < src/main/resources/database.sql
```

4. 当前配置文件 `src/main/resources/db.properties`：
```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=************************************************************************************************************************
db.username=root
db.password=123456
```

**测试数据库连接：**
- 访问 `db-test.html` 页面进行数据库连接测试

## 使用说明

### 入党申请人管理

1. **添加申请人**
   - 点击"添加申请人"按钮
   - 填写完整的申请人信息
   - 点击"保存"提交

2. **查询申请人**
   - 使用搜索表单进行条件查询
   - 支持按姓名、院系、专业、班级、状态等条件搜索
   - 支持分页浏览

3. **编辑申请人**
   - 点击表格中的"编辑"按钮
   - 修改申请人信息
   - 点击"保存"更新

4. **批量操作**
   - 选择多个申请人
   - 可进行批量审核或删除操作

## 开发说明

### 代码规范

- 使用驼峰命名法
- 类名首字母大写
- 方法名和变量名首字母小写
- 常量使用全大写字母

### 扩展开发

如需添加新的功能模块，请按照以下步骤：

1. 在 `entity` 包中创建实体类
2. 在 `dao` 包中创建DAO接口和实现类
3. 在 `service` 包中创建Service接口和实现类
4. 在 `servlet` 包中创建Servlet类
5. 创建对应的前端页面和JavaScript文件

## 测试

### JSP动态页面测试
访问以下JSP页面进行功能测试：

**主要测试页面：**
- `test.jsp` - 综合功能测试，包含服务器信息、会话管理
- `db-test.jsp` - 实时数据库连接测试和表统计
- `java-error-check.jsp` - Java环境检查和类加载测试
- `test-fixes.jsp` - Bug修复验证和JSP功能测试

**测试功能：**
- 数据库连接测试
- JSON工具测试
- 表单验证测试
- 前端页面测试
- 服务器端渲染测试
- 会话状态管理测试
- 实时数据显示测试

### Bug修复验证测试
- JavaScript模块加载测试
- 命名空间冲突测试
- CSS状态样式测试
- 消息提示系统测试
- 工具函数测试
- JSP动态内容测试

### 修复内容
本项目已修复以下重要bug：
- ✅ JavaScript模块加载冲突
- ✅ 全局变量命名冲突
- ✅ CSS状态样式不完整
- ✅ JsonUtil类导入问题
- ✅ Servlet响应处理问题
- ✅ 模态框事件处理问题
- ✅ 模块切换显示问题

详细修复报告请查看 `Bug修复报告.md` 文件。

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库配置信息是否正确
- 检查JDBC驱动是否正确添加

### 2. 页面显示异常
- 检查浏览器控制台是否有JavaScript错误
- 确认CSS和JS文件路径是否正确
- 检查字符编码设置

### 3. 中文乱码问题
- 确保数据库字符集为utf8mb4
- 检查web.xml中的字符编码过滤器配置
- 确认页面meta标签设置正确

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目仅用于学习和教育目的。

## 联系方式

如有问题，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 项目地址: [项目GitHub地址]

---

**注意**: 这是一个教学项目，主要用于演示JavaWeb开发技术。在生产环境中使用前，请进行充分的安全性测试和性能优化。
