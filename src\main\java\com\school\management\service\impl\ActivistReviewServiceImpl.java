package com.school.management.service.impl;

import com.school.management.dto.ActivistReviewDTO;
import com.school.management.entity.PartyApplicant;
import com.school.management.entity.PartyActivist;
import com.school.management.service.ActivistReviewService;
import com.school.management.service.PartyApplicantService;
import com.school.management.service.impl.PartyApplicantServiceImpl;
import com.school.management.dao.PartyActivistDAO;
import com.school.management.dao.impl.PartyActivistDAOImpl;

/**
 * 积极分子审议服务实现类
 */
public class ActivistReviewServiceImpl implements ActivistReviewService {
    
    private PartyApplicantService applicantService;
    private PartyActivistDAO activistDAO;
    
    public ActivistReviewServiceImpl() {
        this.applicantService = new PartyApplicantServiceImpl();
        this.activistDAO = new PartyActivistDAOImpl();
    }
    
    @Override
    public PartyActivist convertToActivist(ActivistReviewDTO reviewDTO) throws Exception {
        // 1. 验证输入参数
        if (reviewDTO == null || reviewDTO.getApplicantId() == null) {
            throw new IllegalArgumentException("审议信息不能为空");
        }
        
        if (reviewDTO.getActivistDate() == null) {
            throw new IllegalArgumentException("确认积极分子时间不能为空");
        }
        
        if (reviewDTO.getBranchSecretary() == null || reviewDTO.getBranchSecretary().trim().isEmpty()) {
            throw new IllegalArgumentException("支部书记不能为空");
        }
        
        // 2. 验证申请人是否可以转换
        if (!validateApplicantForConversion(reviewDTO.getApplicantId())) {
            throw new Exception("申请人不符合转换条件");
        }
        
        // 3. 检查是否已经是积极分子
        if (isAlreadyActivist(reviewDTO.getApplicantId())) {
            throw new Exception("该申请人已经是积极分子");
        }
        
        // 4. 获取申请人信息
        PartyApplicant applicant = getApplicantForReview(reviewDTO.getApplicantId());
        if (applicant == null) {
            throw new Exception("未找到申请人信息");
        }
        
        try {
            // 5. 创建积极分子对象
            PartyActivist activist = new PartyActivist(
                applicant, 
                reviewDTO.getActivistDate(),
                reviewDTO.getBranchSecretary(),
                reviewDTO.getHasLeagueRecommendation()
            );
            
            if (reviewDTO.getRemarks() != null) {
                activist.setRemarks(reviewDTO.getRemarks());
            }
            
            // 6. 保存积极分子信息
            PartyActivist savedActivist = activistDAO.insertAndReturn(activist);
            if (savedActivist == null) {
                throw new Exception("保存积极分子信息失败");
            }
            
            // 7. 更新申请人状态为"已转积极分子"
            applicant.setStatus("已转积极分子");
            applicantService.update(applicant);
            
            System.out.println("成功将申请人转为积极分子: " + applicant.getName());
            return savedActivist;
            
        } catch (Exception e) {
            System.err.println("转换积极分子失败: " + e.getMessage());
            throw new Exception("转换积极分子失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean validateApplicantForConversion(Integer applicantId) {
        try {
            PartyApplicant applicant = applicantService.getById(applicantId);
            if (applicant == null) {
                System.err.println("申请人不存在: " + applicantId);
                return false;
            }
            
            // 检查申请人状态是否为"已通过"
            if (!"已通过".equals(applicant.getStatus())) {
                System.err.println("申请人状态不是'已通过': " + applicant.getStatus());
                return false;
            }
            
            // 检查必要信息是否完整
            if (applicant.getName() == null || applicant.getName().trim().isEmpty()) {
                System.err.println("申请人姓名为空");
                return false;
            }
            
            if (applicant.getIdCard() == null || applicant.getIdCard().trim().isEmpty()) {
                System.err.println("申请人身份证号为空");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("验证申请人失败: " + e.getMessage());
            return false;
        }
    }
    
    @Override
    public PartyApplicant getApplicantForReview(Integer applicantId) {
        try {
            return applicantService.getById(applicantId);
        } catch (Exception e) {
            System.err.println("获取申请人信息失败: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public boolean isAlreadyActivist(Integer applicantId) {
        try {
            // 通过申请人ID查询是否已存在积极分子记录
            return activistDAO.existsByApplicantId(applicantId);
        } catch (Exception e) {
            System.err.println("检查积极分子状态失败: " + e.getMessage());
            return false;
        }
    }
}
