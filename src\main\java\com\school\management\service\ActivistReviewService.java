package com.school.management.service;

import com.school.management.dto.ActivistReviewDTO;
import com.school.management.entity.PartyApplicant;
import com.school.management.entity.PartyActivist;

/**
 * 积极分子审议服务接口
 */
public interface ActivistReviewService {
    
    /**
     * 将入党申请人转为积极分子
     * @param reviewDTO 审议信息
     * @return 转换后的积极分子对象
     * @throws Exception 转换失败时抛出异常
     */
    PartyActivist convertToActivist(ActivistReviewDTO reviewDTO) throws Exception;
    
    /**
     * 验证申请人是否可以转为积极分子
     * @param applicantId 申请人ID
     * @return 验证结果
     */
    boolean validateApplicantForConversion(Integer applicantId);
    
    /**
     * 获取申请人信息用于审议
     * @param applicantId 申请人ID
     * @return 申请人信息
     */
    PartyApplicant getApplicantForReview(Integer applicantId);
    
    /**
     * 检查申请人是否已经是积极分子
     * @param applicantId 申请人ID
     * @return 是否已经是积极分子
     */
    boolean isAlreadyActivist(Integer applicantId);
}
