<component name="ArtifactManager">
  <artifact type="exploded-war" name="Management:Web exploded">
    <output-path>$PROJECT_DIR$/out/artifacts/Management_Web_exploded</output-path>
    <root id="root">
      <element id="javaee-facet-resources" facet="Management/web/Web" />
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="Management" />
        </element>
      </element>
    </root>
  </artifact>
</component>