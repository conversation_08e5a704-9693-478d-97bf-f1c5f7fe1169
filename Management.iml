<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/src/main/webapp/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/src/main/webapp" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web3">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web2">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/host-manager/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/host-manager" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web5">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/examples/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/examples" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web4">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/conf/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/conf" relative="/WEB-INF" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web7">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/manager/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/manager" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web6">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/ROOT/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/ROOT" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web8">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/appdev/sample/web/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/appdev/sample/web" relative="/" />
        </webroots>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Java EE 6-Java EE 6" level="project" />
    <orderEntry type="library" name="mysql-connector-j-8.0.33" level="project" />
  </component>
</module>