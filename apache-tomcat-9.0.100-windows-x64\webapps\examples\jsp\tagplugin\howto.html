<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<html>
  <head>
    <title>Tag Plugin Implementation</title>
  </head>
  <body>
    <h2>How to write tag plugins</h2>
    <p>
      To write a plugin, you'll need to download the source for Tomcat.
      There are two steps:
    <ol>
      <li>
        Implement the plugin class.<p/>
        This class, which implements
        <tt>org.apache.jasper.compiler.tagplugin.TagPlugin</tt>
        instructs <PERSON> what Java codes to generate in place of the tag
        handler calls.
        See Javadoc for <tt>org.apache.jasper.compiler.tagplugin.TagPlugin</tt>
        for details.
      </li>

      <li>
        Create the plugin descriptor file <tt> WEB-INF/tagPlugins.xml</tt><p/>
        This file
        specifies the plugin classes and their corresponding tag handler
        classes.
      </li>
    </ol>
  </body>
</html>
